-- Migration pour créer le système de permissions RBAC
-- Version: V002
-- Description: Création des tables permissions, user_permissions et role_permissions

-- 1. <PERSON><PERSON><PERSON> la table permissions
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. C<PERSON>er la table de liaison user_permissions (many-to-many User <-> Permission)
CREATE TABLE IF NOT EXISTS user_permissions (
    user_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (user_id, permission_id),
    FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 3. <PERSON><PERSON><PERSON> la table de liaison role_permissions (many-to-many Role <-> Permission)
CREATE TABLE IF NOT EXISTS role_permissions (
    role_name VARCHAR(50) NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (role_name, permission_id),
    FOREIGN KEY (role_name) REFERENCES roles(name) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 4. Créer des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON user_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_name ON role_permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);

-- 5. Insérer les permissions par défaut du système
INSERT INTO permissions (name, description) VALUES 
    ('READ_USERS', 'Lire les informations des utilisateurs'),
    ('WRITE_USERS', 'Créer et modifier les utilisateurs'),
    ('DELETE_USERS', 'Supprimer les utilisateurs'),
    ('READ_ROLES', 'Lire les informations des rôles'),
    ('WRITE_ROLES', 'Créer et modifier les rôles'),
    ('DELETE_ROLES', 'Supprimer les rôles'),
    ('READ_PERMISSIONS', 'Lire les informations des permissions'),
    ('WRITE_PERMISSIONS', 'Créer et modifier les permissions'),
    ('DELETE_PERMISSIONS', 'Supprimer les permissions'),
    ('ADMIN_SYSTEM', 'Administration complète du système'),
    ('VIEW_LOGS', 'Consulter les logs système'),
    ('MANAGE_CONFIG', 'Gérer la configuration système')
ON CONFLICT (name) DO NOTHING;

-- 6. Assigner les permissions par défaut aux rôles existants

-- Permissions pour le rôle ADMIN (toutes les permissions)
INSERT INTO role_permissions (role_name, permission_id)
SELECT 'ADMIN', p.id 
FROM permissions p
WHERE p.name IN (
    'READ_USERS', 'WRITE_USERS', 'DELETE_USERS',
    'READ_ROLES', 'WRITE_ROLES', 'DELETE_ROLES',
    'READ_PERMISSIONS', 'WRITE_PERMISSIONS', 'DELETE_PERMISSIONS',
    'ADMIN_SYSTEM', 'VIEW_LOGS', 'MANAGE_CONFIG'
)
ON CONFLICT (role_name, permission_id) DO NOTHING;

-- Permissions pour le rôle USER (permissions de base)
INSERT INTO role_permissions (role_name, permission_id)
SELECT 'USER', p.id 
FROM permissions p
WHERE p.name IN ('READ_USERS')
ON CONFLICT (role_name, permission_id) DO NOTHING;

-- 7. Créer une fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Créer le trigger pour la table permissions
CREATE TRIGGER update_permissions_updated_at 
    BEFORE UPDATE ON permissions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Fin de la migration V002
