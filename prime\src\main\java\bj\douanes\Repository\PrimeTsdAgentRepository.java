package bj.douanes.Repository;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.PrimeTsdAgent;

@Repository
public interface PrimeTsdAgentRepository extends JpaRepository<PrimeTsdAgent, Long>{

    List<PrimeTsdAgent> findByrAgentRepartition_Repartition_IdRepartition(Long idRepartition);

    //supprimer primeTsdAgent par idRepartition
    @Modifying
    @Query("DELETE FROM PrimeTsdAgent p WHERE p.rAgentRepartition.id.idRepartition = :idRepartition")
    void deleteByRepartitionId(@Param("idRepartition") Long idRepartition);

}
