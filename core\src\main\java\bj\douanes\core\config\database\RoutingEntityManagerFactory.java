package bj.douanes.core.config.database;

import java.util.HashMap;
import java.util.Map;

import jakarta.persistence.Cache;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.PersistenceUnitUtil;
import jakarta.persistence.Query;
import jakarta.persistence.SynchronizationType;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.metamodel.Metamodel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RoutingEntityManagerFactory implements EntityManagerFactory {

    private final Map<Object, EntityManagerFactory> targetEntityManagerFactories;
    private final EntityManagerFactory defaultTargetEntityManagerFactory;

    public RoutingEntityManagerFactory(Map<Object, EntityManagerFactory> targetEntityManagerFactories,
            EntityManagerFactory defaultTargetEntityManagerFactory) {
        this.targetEntityManagerFactories = new HashMap<>(targetEntityManagerFactories);
        this.defaultTargetEntityManagerFactory = defaultTargetEntityManagerFactory;
    }

    protected EntityManagerFactory determineTargetEntityManagerFactory() {
        String sourceType = DatabaseContextHolder.peekDataSource().orElse(null);
        if (sourceType == null) {
            log.debug("Utilisation de l'EntityManagerFactory par défaut pour les prochaines transactions.");
            return defaultTargetEntityManagerFactory;
        }

        log.info("Choix de l'EntityManagerFactory {} pour les prochaines transactions.", sourceType);
        return targetEntityManagerFactories.get(sourceType);
    }

    @Override
    public EntityManager createEntityManager() {
        return determineTargetEntityManagerFactory().createEntityManager();
    }

    @Override
    public EntityManager createEntityManager(@SuppressWarnings("rawtypes") Map map) {
        return determineTargetEntityManagerFactory().createEntityManager(map);
    }

    @Override
    public EntityManager createEntityManager(SynchronizationType synchronizationType) {
        return determineTargetEntityManagerFactory().createEntityManager(synchronizationType);
    }

    @Override
    public EntityManager createEntityManager(SynchronizationType synchronizationType,
            @SuppressWarnings("rawtypes") Map map) {
        return determineTargetEntityManagerFactory().createEntityManager(synchronizationType, map);
    }

    @Override
    public CriteriaBuilder getCriteriaBuilder() {
        return determineTargetEntityManagerFactory().getCriteriaBuilder();
    }

    @Override
    public Metamodel getMetamodel() {
        return determineTargetEntityManagerFactory().getMetamodel();
    }

    @Override
    public boolean isOpen() {
        return determineTargetEntityManagerFactory().isOpen();
    }

    @Override
    public void close() {
        for (EntityManagerFactory emf : targetEntityManagerFactories.values()) {
            if (emf.isOpen()) {
                emf.close();
            }
        }
        if (defaultTargetEntityManagerFactory.isOpen()) {
            defaultTargetEntityManagerFactory.close();
        }
    }

    @Override
    public Map<String, Object> getProperties() {
        return determineTargetEntityManagerFactory().getProperties();
    }

    @Override
    public Cache getCache() {
        return determineTargetEntityManagerFactory().getCache();
    }

    @Override
    public PersistenceUnitUtil getPersistenceUnitUtil() {
        return determineTargetEntityManagerFactory().getPersistenceUnitUtil();
    }

    @Override
    public void addNamedQuery(String name, Query query) {
        determineTargetEntityManagerFactory().addNamedQuery(name, query);
    }

    @Override
    public <T> T unwrap(Class<T> cls) {
        return determineTargetEntityManagerFactory().unwrap(cls);
    }

    @Override
    public <T> void addNamedEntityGraph(String graphName, jakarta.persistence.EntityGraph<T> entityGraph) {
        determineTargetEntityManagerFactory().addNamedEntityGraph(graphName, entityGraph);
    }
}
