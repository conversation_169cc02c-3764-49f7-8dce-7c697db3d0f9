package bj.douanes.Services;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.Model.TypePrime;
import bj.douanes.Repository.PrimesRepo;
import lombok.RequiredArgsConstructor;

public interface PrimeServ {
    List<TypePrime> getAllPrimes();
    TypePrime getPrimeById(String id);
    TypePrime createPrime(TypePrime prime);
    TypePrime updatePrime(String id, TypePrime prime);
    void deletePrime(String id);
}

@RequiredArgsConstructor
@Service
class InnerPrimeServ implements PrimeServ{
    private final PrimesRepo primeRepo;

    @Override
    public List<TypePrime> getAllPrimes() {
        return primeRepo.findAll();
    }

    @Override
    public TypePrime getPrimeById(String id) {
        return primeRepo.findById(id).orElse(null);
    }
    @Override
    public TypePrime createPrime(TypePrime prime) {
        return primeRepo.save(prime);
    }
    @Override
    public TypePrime updatePrime(String id, TypePrime prime) {
        TypePrime primeToUpdate = primeRepo.findById(id).orElse(null);
        if(primeToUpdate == null){
            return null;
        }
        primeToUpdate.setLibelle(prime.getLibelle());
        primeToUpdate.setDescription(prime.getDescription());
        primeToUpdate.setPeriodicite(prime.getPeriodicite());
        primeToUpdate.setNom_table_prime(prime.getNom_table_prime());
        primeToUpdate.setAvecUnite(prime.getAvecUnite());
        return primeRepo.save(primeToUpdate);
    }
    @Override
    public void deletePrime(String id) {
        primeRepo.deleteById(id);
    }
}