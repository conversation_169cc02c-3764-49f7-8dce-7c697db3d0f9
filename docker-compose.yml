version: '3.8'

services:
  # Application ERP Douanes
  erp-backend:
    build: .
    container_name: ${DOCKER_CONTAINER_NAME:-backend}
    ports:
      - "${DOCKER_HOST_PORT:-8082}:${DOCKER_CONTAINER_PORT:-80}"
    environment:
      # Configuration Spring
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-home}
      
      # Configuration bases de données
      - DB_DGD_USERNAME=${DB_DGD_USERNAME:-root}
      - DB_DGD_PASSWORD=${DB_DGD_PASSWORD}
      - DB_DGD_URL=${DB_DGD_URL:-***************************************}
      
      - DB_HOME_USERNAME=${DB_HOME_USERNAME:-root}
      - DB_HOME_PASSWORD=${DB_HOME_PASSWORD}
      - DB_HOME_URL=${DB_HOME_URL:-*****************************************}
      
      - DB_TEST_USERNAME=${DB_TEST_USERNAME:-root}
      - DB_TEST_PASSWORD=${DB_TEST_PASSWORD}
      - DB_TEST_URL=${DB_TEST_URL:-*****************************************}
      
      # Configuration JWT
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_DELAY=${JWT_DELAY:-24}
      
      # Configuration Mail
      - MAIL_HOST=${MAIL_HOST:-mail.finances.bj}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      
      # Configuration sécurité
      - SECURITY_WHITE_LIST=${SECURITY_WHITE_LIST:-/**}
      
      # Configuration logs
      - LOG_LEVEL_DOUANES=${LOG_LEVEL_DOUANES:-debug}
      - LOG_LEVEL_ROOT=${LOG_LEVEL_ROOT:-info}
    
    depends_on:
      - postgres-dgd
      - postgres-home
      - postgres-test
    
    restart: unless-stopped
    
    networks:
      - erp-network

  # Base de données PostgreSQL pour DGD
  postgres-dgd:
    image: postgres:15-alpine
    container_name: postgres-dgd
    environment:
      - POSTGRES_DB=dgd
      - POSTGRES_USER=${DB_DGD_USERNAME:-root}
      - POSTGRES_PASSWORD=${DB_DGD_PASSWORD:-root}
    volumes:
      - postgres_dgd_data:/var/lib/postgresql/data
      - ./docker/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - erp-network

  # Base de données PostgreSQL pour HOME
  postgres-home:
    image: postgres:15-alpine
    container_name: postgres-home
    environment:
      - POSTGRES_DB=home
      - POSTGRES_USER=${DB_HOME_USERNAME:-root}
      - POSTGRES_PASSWORD=${DB_HOME_PASSWORD:-root}
    volumes:
      - postgres_home_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - erp-network

  # Base de données PostgreSQL pour TEST
  postgres-test:
    image: postgres:15-alpine
    container_name: postgres-test
    environment:
      - POSTGRES_DB=test
      - POSTGRES_USER=${DB_TEST_USERNAME:-root}
      - POSTGRES_PASSWORD=${DB_TEST_PASSWORD:-root}
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - erp-network

  # pgAdmin pour la gestion des bases de données (optionnel)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - erp-network
    profiles:
      - tools

volumes:
  postgres_dgd_data:
  postgres_home_data:
  postgres_test_data:
  pgadmin_data:

networks:
  erp-network:
    driver: bridge
