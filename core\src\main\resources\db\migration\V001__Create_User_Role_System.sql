-- Migration pour créer le nouveau système d'utilisateurs et de rôles
-- Version: V001
-- Description: Création des tables utilisateurs et roles avec relation one-to-many (un utilisateur = un rôle)

-- 0. Activer l'extension UUID si elle n'est pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

DROP TABLE IF EXISTS utilisateurs;

-- 1. C<PERSON>er la table utilisateurs avec référence directe au rôle
CREATE TABLE IF NOT EXISTS utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    matricule VARCHAR(255),
    role_name VARCHAR(50), -- Référence directe au rôle (un utilisateur = un rôle)
    is_suspended BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON> la table roles
CREATE TABLE IF NOT EXISTS roles (
    name VARCHAR(50) PRIMARY KEY,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Ajouter la contrainte de clé étrangère pour la relation utilisateur-rôle
ALTER TABLE utilisateurs
ADD CONSTRAINT fk_user_role
FOREIGN KEY (role_name) REFERENCES roles(name) ON DELETE SET NULL;

-- 4. Insérer les rôles par défaut
INSERT INTO roles (name, description) VALUES
    ('ADMIN', 'Administrateur avec tous les privilèges'),
    ('USER', 'Utilisateur standard avec accès de base')
ON CONFLICT (name) DO NOTHING;

-- 5. Créer un index pour améliorer les performances des requêtes sur role_name
CREATE INDEX IF NOT EXISTS idx_utilisateurs_role_name ON utilisateurs(role_name);

-- 6. Ajouter un utilisateur admin par défaut pour les tests (optionnel)
-- Mot de passe: admin123 (hashé avec BCrypt)
INSERT INTO utilisateurs (email, password, matricule, role_name) VALUES
    ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GFg.3C.', 'ADMIN001', 'ADMIN')
ON CONFLICT (email) DO NOTHING;

-- Fin
