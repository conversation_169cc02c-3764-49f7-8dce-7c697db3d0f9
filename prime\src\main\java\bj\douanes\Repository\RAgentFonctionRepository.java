package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.RAgentFonction;
import bj.douanes.Model.RAgentFonctionId;

@Repository
public interface RAgentFonctionRepository extends JpaRepository<RAgentFonction, RAgentFonctionId>{


    @Query("SELECT ru FROM RAgentFonction ru WHERE ru.id.matricule = :matricule AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    List<RAgentFonction> findAllByMatricule(String matricule);

     //Requette pour recuperer la fonction d'un agent via son matricule
    @Query("SELECT ru.id.codeFonction FROM RAgentFonction ru WHERE ru.id.matricule = :matricule AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    String findCodeFonctionByMatricule(String matricule);

    // Requête personnalisée pour trouver un agent par ragentFonctionId
    @Query("SELECT ru FROM RAgentFonction ru WHERE ru.id = :id AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    RAgentFonction findAgentById(RAgentFonctionId id);

}
