package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.EmailFacade;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/email")
public class EmailController {

    private final EmailFacade emailFacade;

    @PostMapping("/{idRepartition}")
    public ResponseEntity<?> sendOrdreVirementEmailToAllAgents(@PathVariable Long idRepartition) {
        emailFacade.sendOrdreVirementEmailToAllAgents(idRepartition);
        return AppResponse.ok("Emails sent successfully");
    }
    
}
