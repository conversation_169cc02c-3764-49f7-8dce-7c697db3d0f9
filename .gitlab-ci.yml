stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE: "erp-backend:latest"
  DOCKER_CONTAINER_NAME: "backend"
  DOCKER_HOST_PORT: "8082"
  DOCKER_CONTAINER_PORT: "80"

# Stage de build (optionnel, pour les tests)
build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    paths:
      - .m2/repository
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - echo "Building application..."
    - mvn clean compile -DskipTests


# Déploiement en production
deploy_prod:
  stage: deploy
  tags:
    - docker-socket
  variables:
    SPRING_PROFILES_ACTIVE: "prod"
  script:
    - echo "Deploying to production..."

    # Construction de l'image Docker
    - docker build -t $DOCKER_IMAGE .

    # Arrêt et suppression du conteneur existant
    - docker stop $DOCKER_CONTAINER_NAME || true
    - docker rm $DOCKER_CONTAINER_NAME || true

    # Démarrage du nouveau conteneur avec les variables d'environnement
    - |
      docker run -d \
        --restart=always \
        -p $DOCKER_HOST_PORT:$DOCKER_CONTAINER_PORT \
        --name $DOCKER_CONTAINER_NAME \
        -e SPRING_PROFILES_ACTIVE=$SPRING_PROFILES_ACTIVE \
        -e DB_DGD_USERNAME=$DB_DGD_USERNAME \
        -e DB_DGD_PASSWORD=$DB_DGD_PASSWORD \
        -e DB_DGD_URL=$DB_DGD_URL \
        -e DB_HOME_USERNAME=$DB_HOME_USERNAME \
        -e DB_HOME_PASSWORD=$DB_HOME_PASSWORD \
        -e DB_HOME_URL=$DB_HOME_URL \
        -e DB_TEST_USERNAME=$DB_TEST_USERNAME \
        -e DB_TEST_PASSWORD=$DB_TEST_PASSWORD \
        -e DB_TEST_URL=$DB_TEST_URL \
        -e JWT_SECRET_KEY=$JWT_SECRET_KEY \
        -e JWT_DELAY=$JWT_DELAY \
        -e MAIL_HOST=$MAIL_HOST \
        -e MAIL_PORT=$MAIL_PORT \
        -e MAIL_USERNAME=$MAIL_USERNAME \
        -e MAIL_PASSWORD=$MAIL_PASSWORD \
        -e SECURITY_WHITE_LIST="$SECURITY_WHITE_LIST" \
        -e LOG_LEVEL_DOUANES=$LOG_LEVEL_DOUANES \
        -e LOG_LEVEL_ROOT=$LOG_LEVEL_ROOT \
        $DOCKER_IMAGE

    # Vérification du déploiement
    - echo "Waiting for application to start..."
    - sleep 10
    - docker logs $DOCKER_CONTAINER_NAME --tail 20

    # Test de santé (optionnel)
    - echo "Testing application health..."
    - curl -f http://localhost:$DOCKER_HOST_PORT/actuator/health || echo "Health check failed"

  only:
    - main
  environment:
    name: production


# # Déploiement en staging (optionnel)
# deploy_staging:
#   stage: deploy
#   tags:
#     - docker-socket
#   variables:
#     SPRING_PROFILES_ACTIVE: "home"
#     DOCKER_CONTAINER_NAME: "backend-staging"
#     DOCKER_HOST_PORT: "8083"
#   script:
#     - echo "Deploying to staging..."
#     - docker build -t $DOCKER_IMAGE-staging .
#     - docker stop $DOCKER_CONTAINER_NAME || true
#     - docker rm $DOCKER_CONTAINER_NAME || true
#     - |
#       docker run -d \
#         -p $DOCKER_HOST_PORT:$DOCKER_CONTAINER_PORT \
#         --restart=always \
#         --name $DOCKER_CONTAINER_NAME \
#         -e SPRING_PROFILES_ACTIVE=$SPRING_PROFILES_ACTIVE \
#         -e DB_DGD_USERNAME=$DB_DGD_USERNAME \
#         -e DB_DGD_PASSWORD=$DB_DGD_PASSWORD \
#         -e DB_HOME_USERNAME=$DB_HOME_USERNAME \
#         -e DB_HOME_PASSWORD=$DB_HOME_PASSWORD \
#         -e DB_TEST_USERNAME=$DB_TEST_USERNAME \
#         -e DB_TEST_PASSWORD=$DB_TEST_PASSWORD \
#         -e JWT_SECRET_KEY=$JWT_SECRET_KEY \
#         -e JWT_DELAY=$JWT_DELAY \
#         -e MAIL_USERNAME=$MAIL_USERNAME \
#         -e MAIL_PASSWORD=$MAIL_PASSWORD \
#         $DOCKER_IMAGE-staging
#   only:
#     - develop
#   environment:
#     name: staging
#     url: http://localhost:8083
#   when: manual
