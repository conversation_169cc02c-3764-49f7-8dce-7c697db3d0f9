package bj.douanes.core.auth.dto;

import java.util.Set;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserPermissionDto {
    
    private UUID userId;
    private String email;
    private String matricule;
    
    // Rôle de l'utilisateur
    private String role;
    
    // Permissions directes de l'utilisateur
    private Set<String> directPermissions;
    
    // Permissions héritées des rôles
    private Set<String> rolePermissions;
    
    // Toutes les permissions (directes + rôles)
    private Set<String> allPermissions;
}
