package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.CertifFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/diplome")
public class CertificationController {
 
    @Autowired
    private CertifFacade certifFacade ;

    @GetMapping
    public ResponseEntity<?> getAllCertification() {
        return AppResponse.ok(certifFacade.getCertifAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getCertById(@PathVariable Long id) {
        return AppResponse.ok(certifFacade.getCertifById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> addCertif(@RequestBody DTO.CertificationDto certification, @PathVariable Long agentId) {
        return AppResponse.created(certifFacade.createCertif(agentId, certification));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateCertif(@RequestBody DTO.CertificationDto certification,@PathVariable Long id){
        return AppResponse.ok(certifFacade.update(id, certification));
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteAgent(@PathVariable Long id){
        return AppResponse.ok(certifFacade.deleteCertif(id)); 
    }
}
