package bj.douanes.Repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.DTO.BanqueInfoDTO;
import bj.douanes.DTO.BanqueInfoDTO2;
import bj.douanes.DTO.BanqueInfoDTO3;
import bj.douanes.Model.RAgentBanque;
import bj.douanes.Model.RAgentBanqueId;

@Repository
public interface RAgentBanqueRepository extends JpaRepository<RAgentBanque, RAgentBanqueId>{
    
    @Query("SELECT ru FROM RAgentBanque ru WHERE ru.id.matricule = :matricule AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    List<RAgentBanque> findAllByMatricule(String matricule); 
    
    // Requette pour recuperer la banque d'un agent via son matricule
    @Query("SELECT new bj.douanes.DTO.BanqueInfoDTO(ru.id.codeBanque, ru.rib) FROM RAgentBanque ru WHERE ru.id.matricule = :matricule AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    List<BanqueInfoDTO> findBanqueInfosByMatricule(String matricule);

    @Query("SELECT new bj.douanes.DTO.BanqueInfoDTO2(ru.id.codeBanque, ru.rib, b.nomBanque) FROM RAgentBanque ru JOIN ru.banque b WHERE ru.id.matricule = :matricule AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    List<BanqueInfoDTO2> findBanqueInfosByMatricule2(String matricule);

    @Query("SELECT new bj.douanes.DTO.BanqueInfoDTO3(ru.id.matricule, ru.rib) FROM RAgentBanque ru JOIN ru.banque b WHERE ru.id.matricule IN :matricules AND (ru.dateFin IS NULL OR ru.dateFin > CURRENT_DATE)")
    List<BanqueInfoDTO3> findBanqueInfosByMatricules(@Param("matricules") List<String> matricules);

    // Requête personnalisée pour trouver un agent par ragentBanqueId
    @Query("SELECT ru FROM RAgentBanque ru WHERE ru.id = :id")
    RAgentBanque findAgentById(@Param("id") RAgentBanqueId id);
}
