package bj.douanes.core.auth.repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.core.auth.model.Permission;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, UUID> {
    
    /**
     * Recherche une permission par son nom
     */
    Optional<Permission> findByName(String name);
    
    /**
     * Vérifie si une permission existe par son nom
     */
    boolean existsByName(String name);
    
    /**
     * Recherche des permissions par leurs noms
     */
    List<Permission> findByNameIn(Set<String> names);
    
    /**
     * Recherche toutes les permissions contenant un terme dans le nom ou la description
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Permission> findByNameOrDescriptionContainingIgnoreCase(@Param("searchTerm") String searchTerm);
    
    /**
     * Recherche les permissions d'un utilisateur spécifique
     */
    @Query("SELECT p FROM Permission p JOIN p.users u WHERE u.id = :userId")
    List<Permission> findByUserId(@Param("userId") UUID userId);
    
    /**
     * Recherche les permissions par défaut d'un rôle
     */
    @Query("SELECT p FROM Permission p JOIN p.roles r WHERE r.name = :roleName")
    List<Permission> findByRoleName(@Param("roleName") String roleName);
    
    /**
     * Recherche toutes les permissions triées par nom
     */
    List<Permission> findAllByOrderByNameAsc();
    
    /**
     * Compte le nombre d'utilisateurs ayant une permission spécifique
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.permissions p WHERE p.name = :permissionName")
    Long countUsersByPermissionName(@Param("permissionName") String permissionName);
    
    /**
     * Recherche les permissions non assignées à un rôle spécifique
     */
    @Query("SELECT p FROM Permission p WHERE p NOT IN " +
           "(SELECT rp FROM Permission rp JOIN rp.roles r WHERE r.name = :roleName)")
    List<Permission> findPermissionsNotInRole(@Param("roleName") String roleName);
    
    /**
     * Recherche les permissions non assignées à un utilisateur spécifique
     */
    @Query("SELECT p FROM Permission p WHERE p NOT IN " +
           "(SELECT up FROM Permission up JOIN up.users u WHERE u.id = :userId)")
    List<Permission> findPermissionsNotAssignedToUser(@Param("userId") UUID userId);
}
