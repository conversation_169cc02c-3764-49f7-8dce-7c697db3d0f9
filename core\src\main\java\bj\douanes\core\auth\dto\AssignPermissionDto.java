package bj.douanes.core.auth.dto;

import java.util.Set;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssignPermissionDto {
    
    @NotNull(message = "L'ID de l'utilisateur est obligatoire")
    private UUID userId;
    
    @NotEmpty(message = "Au moins une permission doit être spécifiée")
    private Set<String> permissionNames;
}
