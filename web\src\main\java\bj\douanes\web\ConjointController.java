package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.ConjointFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/conjoint")
public class ConjointController {
    
    @Autowired
    private ConjointFacade conjointFacade;

    @GetMapping
    public ResponseEntity<?> getAllConjoint() {
        return AppResponse.ok(conjointFacade.getConjointAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getConjointById(@PathVariable Long id) {
        return AppResponse.ok(conjointFacade.getConjointById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createAgentConjoint(@PathVariable Long agentId,@RequestBody DTO.ConjointDto conjointDto) {
        return AppResponse.created(conjointFacade.createConjoint(agentId, conjointDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatedAgentConjoint(@RequestBody DTO.ConjointDto conjointDto,@PathVariable Long id){
        return AppResponse.ok(conjointFacade.update(id, conjointDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteAgentConjoint(@PathVariable Long id){
        return AppResponse.ok(conjointFacade.deleteConjoint(id)); 
    }
}
