server.port=8082

# Active le mode debug pour tous les logs # logging.level.root=debug # logging.level.org.springframework=debug
logging.level.bj.douanes=debug

app.jwt.secret-key=${JWT_SECRET_KEY:9CC5494122C8898D8419CC54945FFFD4FFED12D8419CC54945FFFD4FFED122C8898D}
app.jwt.delay=${JWT_DELAY:24}

app.security.white-list=${SECURITY_WHITE_LIST:/**}
# app.security.white-list=/,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs \
#                         ,/api/auth/** 
#                         # ,/api/asy/** \
#                         # ,/api/admin/**,/api/transport/** 
#                         # ,/*.*,/static/**,/assets/**,/eservice/** \
#                         # ,/h2-console/** \
#                         # ,/api/agents/**,/api/prime/**



# ===================================== Datasource Configuration =====================================
app.datasources.typeNames=dgd,home,test

app.datasource.dgd.url=${DB_DGD_URL:************************************}
app.datasource.dgd.username=${DB_DGD_USERNAME:root}
app.datasource.dgd.password=${DB_DGD_PASSWORD:}
app.datasource.dgd.packages=bj.douanes.personal,bj.douanes.transport,bj.douanes.core

app.datasource.home.url=${DB_HOME_URL:*************************************}
app.datasource.home.username=${DB_HOME_USERNAME:root}
app.datasource.home.password=${DB_HOME_PASSWORD:}
app.datasource.home.packages=bj.douanes.core,bj.douanes.prime,bj.douanes.Services

app.datasource.test.url=${DB_TEST_URL:*************************************}
app.datasource.test.username=${DB_TEST_USERNAME:root}
app.datasource.test.password=${DB_TEST_PASSWORD:}
app.datasource.test.packages=bj.douanes.sydoniaplusplus

# ===================================== END Datasource Configuration =====================================



# ===================================== Mail Configuration =====================================
# spring.mail.properties.mail.smtp.localhost=127.0.0.1
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.debug=true
spring.mail.host=${MAIL_HOST:mail.finances.bj}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:}
# ===================================== END Mail Configuration =====================================