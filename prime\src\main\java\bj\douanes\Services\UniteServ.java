package bj.douanes.Services;

import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.UniteDTO;
import bj.douanes.Model.Unite;
import bj.douanes.Repository.UniteRepository;
import bj.douanes.core.config.database.WithDatabase;
import lombok.RequiredArgsConstructor;

public interface UniteServ {

    List<UniteDTO> getAllUnites();

    Unite createUnite(Unite unite);

    Unite updateUnite(String codeUnite, Unite unite);

    void deleteUnite(String codeUnite);
}

@Service
@RequiredArgsConstructor
class UniteServImpl implements UniteServ {

    private final UniteRepository uniteRepository;

    @Override
    @WithDatabase("secondary")
    public List<UniteDTO> getAllUnites() {
        return uniteRepository.findAll().stream()
                .map(unite -> new UniteDTO(unite.getCodeUnite(), unite.getLibelle()))
                .toList();
    }

    @Override
    public Unite createUnite(Unite unite) {
        return uniteRepository.save(unite);
    }

    @Override
    public Unite updateUnite(String codeUnite, Unite unite) {
        Unite existingUnite = uniteRepository.findById(codeUnite)
                .orElseThrow(() -> new RuntimeException("Unite not found with code: " + codeUnite));

        existingUnite.setLibelle(unite.getLibelle());
        return uniteRepository.save(existingUnite);
    }

    @Override
    public void deleteUnite(String codeUnite) {
        uniteRepository.deleteById(codeUnite);
    }
}