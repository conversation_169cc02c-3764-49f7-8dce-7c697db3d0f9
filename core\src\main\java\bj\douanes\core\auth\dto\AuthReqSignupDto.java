package bj.douanes.core.auth.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public record AuthReqSignupDto(
        @NotBlank(message = "L'email est obligatoire")
        @Email(message = "Format d'email invalide")
        String email,
        
        @NotBlank(message = "Le mot de passe est obligatoire")
        @Size(min = 5, message = "Le mot de passe doit contenir au moins 5 caractères")
        String password,
        
        @NotBlank(message = "Le matricule est obligatoire")
        String matricule,

        String role
) {

}
