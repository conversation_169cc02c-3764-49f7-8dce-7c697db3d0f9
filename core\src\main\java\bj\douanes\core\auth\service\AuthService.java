package bj.douanes.core.auth.service;

import bj.douanes.core.auth.dto.AuthReqDto;
import bj.douanes.core.auth.dto.AuthReqSigninDto;
import bj.douanes.core.auth.dto.AuthReqSignupDto;
import bj.douanes.core.auth.model.Role;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.shared.error.ApiException;
import bj.douanes.core.shared.jwt.JwtUtils;
import jakarta.transaction.Transactional;

import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import bj.douanes.core.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface AuthService {
    public AuthReqDto signUp(AuthReqSignupDto registrationRequest);

    public AuthReqDto signIn(AuthReqSigninDto signinRequest);
}

@Slf4j
@Service
@RequiredArgsConstructor
class ServiceImpl implements AuthService {

    private final UserRepository userRepository;
    private final JwtUtils jwtUtils;
    private final AuthenticationManager authenticationManager;
    private final PasswordEncoder passwordEncoder;
    private final CrudService roleService;
    private final PermissionService permissionService;
    private final ModelMapper modelMapper;

    @Override
    @Transactional
    public AuthReqDto signUp(AuthReqSignupDto signupRequest) {
        log.info("Tentative d'inscription pour l'email: {}", signupRequest.email());

        // Vérifier si l'utilisateur existe déjà
        if (userRepository.existsByEmailIgnoreCase(signupRequest.email())) {
            log.warn("Tentative d'inscription avec un email deja existant: {}", signupRequest.email());
            throw new ApiException("Un utilisateur avec cet email existe déjà", HttpStatus.CONFLICT);
        }

        try {
            // Créer le nouvel utilisateur
            User newUser = User.builder()
                    .email(signupRequest.email())
                    .password(passwordEncoder.encode(signupRequest.password()))
                    .matricule(signupRequest.matricule())
                    .build();
            // Assigner les rôles
            assignRolesToUser(newUser, signupRequest.role());
            // Sauvegarder l'utilisateur
            User savedUser = userRepository.save(newUser);
            log.info("Utilisateur {} cree avec succes.", savedUser.getEmail());
            // Authentifier l'utilisateur
            Authentication auth = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(signupRequest.email(), signupRequest.password())
            );
            UserDetails user = (UserDetails) auth.getPrincipal();
            // Creation réussie
            log.info("Inscription reussie pour l'utilisateur: {}", user.getUsername());

            // Générer les tokens
            AuthReqDto authReqDto = AuthReqDto.builder()
                .token(jwtUtils.generateToken(user))
                .build();

            // Ajouter les informations de l'utilisateur avec modelmapper
            modelMapper.map(savedUser, authReqDto);
            authReqDto.setRole(savedUser.getRoleName());

            // Ajouter les permissions dans la réponse
            authReqDto.setPermissions(savedUser.getAllPermissionNames());

            return authReqDto;

        } catch (Exception e) {
            log.error("Erreur lors de l'inscription pour l'email: {}", signupRequest.email(), e);
            throw new ApiException("Erreur lors de l'inscription", e);
        }
    }

    @Override
    @Transactional
    public AuthReqDto signIn(AuthReqSigninDto signinRequest) {
        log.info("Tentative de connexion pour l'email: {}", signinRequest.email());

        try {
            // Authentifier l'utilisateur
            Authentication auth = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(signinRequest.email(), signinRequest.password())
            );
            UserDetails user = (UserDetails) auth.getPrincipal();
            // Vérifier si l'utilisateur est suspendu
            if (!user.isEnabled()) {
                log.warn("Tentative de connexion d'un utilisateur suspendu: {}", user.getUsername());
                throw new ApiException("Compte suspendu. Contactez l'administrateur.", HttpStatus.FORBIDDEN);
            } 
            // Connexion réussie
            log.info("Connexion reussie pour l'utilisateur: {}", user.getUsername());
            // Générer les tokens
            return AuthReqDto.builder()
                .token(jwtUtils.generateToken(user))
                .build();

        } catch (BadCredentialsException e) {
            log.warn("Echec de connexion pour l'email: {} - Identifiants invalides", signinRequest.email());
            throw new ApiException("Email ou mot de passe incorrect", HttpStatus.UNAUTHORIZED);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("Erreur lors de la connexion pour l'email: {}", signinRequest.email(), e);
            throw new ApiException("Erreur lors de la connexion", e);
        }
    }

    private void assignRolesToUser(User user, String rolesString) {
        // Initialiser les permissions par défaut si elles n'existent pas
        permissionService.initializeDefaultPermissions();

        Role roleToAssign = null;

        if (rolesString != null) {
            String firstRoleName = rolesString.trim().toUpperCase();
            if (!firstRoleName.isBlank()) {
                roleToAssign = roleService.findRoleByName(firstRoleName).orElse(null);
            }
        }

        // Si aucun rôle valide n'est spécifié, assigner le rôle par défaut USER
        if (roleToAssign == null) {
            roleToAssign = roleService.findRoleByName("USER")
                    .orElseGet(() -> roleService.createRole("USER", "Utilisateur standard avec accès de base"));
        }

        // Assigner le rôle unique à l'utilisateur
        user.setRole(roleToAssign);

        log.info("Utilisateur {} assigné avec le rôle: {} et permissions: {}",
                user.getEmail(), user.getRoleName(), user.getAllPermissionNames());
    }

}