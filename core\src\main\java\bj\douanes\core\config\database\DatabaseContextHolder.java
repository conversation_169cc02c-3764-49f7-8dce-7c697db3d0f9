package bj.douanes.core.config.database;

import java.util.Optional;
import java.util.Stack;

public final class DatabaseContextHolder {

    // Private constructor to prevent instantiation
    private DatabaseContextHolder() {
        throw new UnsupportedOperationException("Utility class");
    }

    private static final ThreadLocal<Stack<String>> CTX = new ThreadLocal<>();

    public static void setCtx(String string) {
        getCtx().push(string);
    }

    public static void restoreCtx() {
        CTX.remove(); // Compliant
    }

    public static Optional<String> peekDataSource() {
        final Stack<String> ctx = getCtx();

        if (ctx.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(ctx.peek());
    }

    private static Stack<String> getCtx() {
        if (CTX.get() == null) {
            CTX.set(new Stack<>());
        }

        return CTX.get();
    }
}
