package bj.douanes.Model;

import java.io.Serializable;
import java.time.LocalDate;

import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Embeddable
public class    RAgentFonctionId implements Serializable{
    
    private String matricule;
    private String codeFonction;
    private LocalDate dateDebut;
}
