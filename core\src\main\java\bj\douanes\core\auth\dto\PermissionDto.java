package bj.douanes.core.auth.dto;

import java.time.LocalDateTime;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PermissionDto {
    
    private UUID id;
    
    @NotBlank(message = "Le nom de la permission est obligatoire")
    @Size(min = 2, max = 100, message = "Le nom de la permission doit contenir entre 2 et 100 caractères")
    private String name;
    
    @Size(max = 255, message = "La description ne peut pas dépasser 255 caractères")
    private String description;
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Nombre d'utilisateurs ayant cette permission directement
    private Long userCount;
    
    // Nombre de rôles ayant cette permission par défaut
    private Long roleCount;
}
