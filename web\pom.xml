<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>bj.douanes</groupId>
		<artifactId>backend</artifactId>
		<version>2.0</version>
	</parent>

	<artifactId>web</artifactId>
	<version>${web.version}</version>
	<name>Web</name>
	<description>Web project</description>

	<dependencies>
		<dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>facade</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>bj.douanes.Application</mainClass>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
					<environmentVariables>
						<SPRING_PROFILES_ACTIVE>${spring.profiles.active}</SPRING_PROFILES_ACTIVE>
					</environmentVariables>
				</configuration>
			</plugin>

			<!-- Plugin pour charger automatiquement le fichier .env -->
			<plugin>
				<groupId>me.fabriciorby</groupId>
				<artifactId>maven-dotenv-plugin</artifactId>
				<version>4.0.0</version>
				<executions>
					<execution>
						<goals>
							<goal>dotenv</goal>
						</goals>
						<configuration>
							<directory>${project.parent.basedir}</directory>
							<filename>.env</filename>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Plugin pour copier les dépendances dans un dossier séparé -->
            <!-- <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin> -->
		</plugins>
	</build>
	
</project>
