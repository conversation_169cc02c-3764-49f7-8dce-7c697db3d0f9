package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.ChildFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/children")
public class ChildController {
    
    @Autowired
    private ChildFacade childFacade;
    
    @GetMapping
    public ResponseEntity<?> getAllChildren() {
        return AppResponse.ok(childFacade.getChildAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getAgentChildById(@PathVariable Long id) {
        return AppResponse.ok(childFacade.getChildById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createAgentChild(@RequestBody DTO.ChildDto childDto, @PathVariable Long agentId) {
        return AppResponse.created(childFacade.createChild(agentId, childDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatedChild(@RequestBody DTO.ChildDto childDto,@PathVariable Long id){
        return AppResponse.ok(childFacade.update(id, childDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteChild(@PathVariable Long id){
        return AppResponse.ok(childFacade.deleteChild(id)); 
    }
}
