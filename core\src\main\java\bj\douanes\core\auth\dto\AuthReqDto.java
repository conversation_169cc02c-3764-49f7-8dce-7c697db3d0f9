package bj.douanes.core.auth.dto;

import java.util.Set;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthReqDto {
    private String token;
    private UUID id;
    private String matricule;
    private String email;
    private String role; // Un seul rôle par utilisateur
    private Set<String> permissions;
    // private String password;
}
