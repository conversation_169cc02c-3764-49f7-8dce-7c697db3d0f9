package bj.douanes.facade;

import java.util.List;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.core.auth.dto.AuthReqDto;
import bj.douanes.core.auth.dto.AuthReqSigninDto;
import bj.douanes.core.auth.dto.AuthReqSignupDto;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.service.AuthService;
import bj.douanes.core.auth.service.CrudService;
import bj.douanes.core.shared.error.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface AuthFacade {
    public AuthReqDto signUp(AuthReqSignupDto signupReq);

    public AuthReqDto signIn(AuthReqSigninDto signinReq);

    List<User> getAllUsers();

    // User getUserById(UUID id);

    // User saveUser(Object user);

    // User updateUser(UUID id, Object user);

    String deleteUser(UUID id);
}

@Slf4j
@Service
@RequiredArgsConstructor
class AuthFacadeImpl implements AuthFacade {

    private final ModelMapper modelMapper;
    private final AuthService authService;
    private final CrudService crudService;

    @Override
    public AuthReqDto signUp(AuthReqSignupDto signupReq) {
        log.info("BEGIN SIGNUP for email: {}", signupReq.email());
        return authService.signUp(signupReq);
    }

    @Override
    public AuthReqDto signIn(AuthReqSigninDto signinReq) {
        log.info("BEGIN SIGNIN for email: {}", signinReq.email());
        return authService.signIn(signinReq);
    }

    @Override
    public List<User> getAllUsers() {
        log.info("BEGIN getAllUsers");
        return crudService.findAllUsers();
    }

    @Override
    public String deleteUser(UUID id) {
        log.info("BEGIN deleteUser");
        crudService.deleteUser(id);
        return "true";
    }

}
