package bj.douanes.Services;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.MontantCollectParUniteDto;
import bj.douanes.DTO.PrimeTSDDTO;
import bj.douanes.Model.PrimeTSD;
import bj.douanes.Model.PrimeTSDUnite;
import bj.douanes.Model.RUniteRepartition;
import bj.douanes.Model.RUniteRepartitionId;
import bj.douanes.Model.Repartition;
import bj.douanes.Model.Unite;
import bj.douanes.Repository.PrimeTSDRepo;
import bj.douanes.Repository.PrimeTsdUniteRepository;
import bj.douanes.Repository.RFonctionTypePrimeRepository;
import bj.douanes.Repository.RUniteRepartitionRepository;
import bj.douanes.Repository.RepartitionRepo;
import bj.douanes.Repository.UniteRepository;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UniteRepartitionEtPrimeServ {
     
    private final RUniteRepartitionRepository rUniteRepo;
    private final PrimeTsdUniteRepository primeUniteRepo;
    private final UniteRepository uniteRepo;
    private final RepartitionRepo repartitionRepo;
    private final RFonctionTypePrimeRepository rFonctionTypePrimeRepository;
    private final PrimeTSDRepo primeTSDRepo;

    @Transactional
    public List<PrimeTSDUnite> createVersementUnites(List<MontantCollectParUniteDto> dtoList, Long idRepartition) {
        Repartition repartition = repartitionRepo.findById(idRepartition)
            .orElseThrow(() -> new EntityNotFoundException("Répartition introuvable"));

        List<PrimeTSDUnite> results = new ArrayList<>();

        for (MontantCollectParUniteDto dto : dtoList) {
            Unite unite = uniteRepo.findById(dto.getCodeUnite())
                .orElseThrow(() -> new EntityNotFoundException("Unité " + dto.getCodeUnite() + " introuvable"));

            RUniteRepartition uniteRepartition = new RUniteRepartition();
            RUniteRepartitionId compositeId = new RUniteRepartitionId(dto.getCodeUnite(), idRepartition);
            uniteRepartition.setId(compositeId);
            uniteRepartition.setUnite(unite);
            uniteRepartition.setRepartition(repartition);
            uniteRepartition.setCreatedAt(LocalDate.now());
            uniteRepartition.setUpdatedAt(LocalDate.now());
            rUniteRepo.save(uniteRepartition);

            PrimeTSDUnite primeUnite = new PrimeTSDUnite();
            primeUnite.setRUniteRepartition(uniteRepartition);
            primeUnite.setMontantVerset(dto.getMontantVerset());
            primeUnite.setOeuvreSocialUnite(dto.getMontantVerset().multiply(BigDecimal.valueOf(0.1)));

            BigDecimal reste = dto.getMontantVerset().subtract(primeUnite.getOeuvreSocialUnite());

            primeUnite.setBonificationUnite(reste.multiply(BigDecimal.valueOf(0.2)));
            primeUnite.setPartUnite(reste.multiply(BigDecimal.valueOf(0.8)));
            BigDecimal cumulCoef = rFonctionTypePrimeRepository.getCumulCoefParUniteEtTypePrime(dto.getCodeUnite());
            primeUnite.setCumulCoef(cumulCoef);

            PrimeTSDUnite savedPrimeUnite = primeUniteRepo.save(primeUnite);

            uniteRepartition.setIdPrimeUnite(savedPrimeUnite.getIdPrimeUnite());
            rUniteRepo.save(uniteRepartition);

            results.add(savedPrimeUnite);
        }

        // Calcul des totaux globaux pour charger PrimeTSD 
        BigDecimal montantOeuvreSocialGlobal = BigDecimal.ZERO;
        BigDecimal partGlobal = BigDecimal.ZERO;
        for (PrimeTSDUnite item : results) {
            montantOeuvreSocialGlobal = montantOeuvreSocialGlobal.add(item.getOeuvreSocialUnite());
            partGlobal = partGlobal.add(item.getPartUnite());
        }
        BigDecimal cumulCoefAllAgents = rFonctionTypePrimeRepository.getCumulCoefTSDDesAgentsActifs();

        PrimeTSD primeTSD = new PrimeTSD();
        primeTSD.setOeuvreSocialGlobal(montantOeuvreSocialGlobal);
        primeTSD.setPartGlobal(partGlobal);
        primeTSD.setCumulCoefGlobal(cumulCoefAllAgents);
        primeTSD.setRepartition(repartition);
        primeTSDRepo.save(primeTSD);

        System.out.println("Montant Oeuvre Sociale Global: " + primeTSD.getOeuvreSocialGlobal());
        System.out.println("Part Global: " + primeTSD.getPartGlobal());
        System.out.println("Cumul Coefficient Global: " + primeTSD.getCumulCoefGlobal());

        return results;
    }


    public List<PrimeTSDDTO> findAllPrime() {        
        //renvoyer la liste avec DTO
        List<PrimeTSD> primes = primeTSDRepo.findAll();
        if (primes.isEmpty()) {
            throw new EntityNotFoundException("Aucune prime TSD trouvée");
        }
        //retouner la liste avec PrimeTSDDTO
        List<PrimeTSDDTO> primeTSDDTOs = new ArrayList<>();
        for (PrimeTSD prime : primes) {
            PrimeTSDDTO dto = new PrimeTSDDTO();
            dto.setOeuvreSocialGlobal(prime.getOeuvreSocialGlobal());
            dto.setPartGlobal(prime.getPartGlobal());
            dto.setCumulCoefGlobal(prime.getCumulCoefGlobal());
            primeTSDDTOs.add(dto);
        }
        return primeTSDDTOs;
    }

    // Méthode pour obtenir la prime TSD par ID
    public PrimeTSDDTO getPrimeTSDById(Long id) {
        PrimeTSD prime = primeTSDRepo.findByRepartitionIdRepartition(id);
        if (prime == null) {
            throw new EntityNotFoundException("Prime TSD introuvable pour l'ID: " + id);
        }     
        PrimeTSDDTO dto = new PrimeTSDDTO();
        dto.setOeuvreSocialGlobal(prime.getOeuvreSocialGlobal());
        dto.setPartGlobal(prime.getPartGlobal());
        dto.setCumulCoefGlobal(prime.getCumulCoefGlobal());
        
        return dto;
    }

}
