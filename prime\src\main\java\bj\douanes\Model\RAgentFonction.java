package bj.douanes.Model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "r_agent_fonction")
public class RAgentFonction {

    @EmbeddedId
    private RAgentFonctionId id;

    private LocalDate dateFin;
    private LocalDate createdAt;
    private LocalDate updatedAt;

    @ManyToOne
    @MapsId("matricule")
    @JoinColumn(name = "matricule")
    @JsonIgnore
    private AgentTSD agent;

    @ManyToOne
    @MapsId("codeFonction")
    @JoinColumn(name = "code_fonction")
    @JsonIgnore
    private Fonction fonction;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDate.now();
        updatedAt = LocalDate.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDate.now();
    }
}