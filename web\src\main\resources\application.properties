spring.application.name=erp_douanes_backend
spring.jpa.show-sql=true
logging.level.sql=debug
# spring.task.scheduling.pool.size=10
#spring.mvc.pathmatch.matching-strategy=ant_path_matcher
#springdoc.swagger-ui.disable-swagger-default-url=true

# spring.jpa.hibernate.ddl-auto=none

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

app.jwt.secret-key=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898E
app.jwt.delay=24

app.security.white-list=/,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs \
                        ,/api/auth/** 
                        # ,/api/asy/** \
                        # ,/api/admin/**,/api/transport/** 
                        # ,/*.*,/static/**,/assets/**,/eservice/** \
                        # ,/h2-console/** \
                        # ,/api/agents/**,/api/prime/**

spring.profiles.active=home