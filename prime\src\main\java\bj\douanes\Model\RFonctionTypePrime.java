package bj.douanes.Model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Entity
@Table(name = "r_fonction_type_prime")
public class RFonctionTypePrime {

    @EmbeddedId
    private RFonctionTypePrimeId id;

    @ManyToOne
    @MapsId("codeFonction")
    @JoinColumn(name = "code_fonction")
    private Fonction fonction;

    @ManyToOne
    @MapsId("codeTypePrime")
    @JoinColumn(name = "code_type_prime")
    @JsonIgnore
    private TypePrime typePrime;

    @Column(name = "date_fin")
    private LocalDate dateFin;

    @Column(name = "coefficient")
    private BigDecimal coefficient;

    @Column(name = "created_at")
    private LocalDate createdAt;

    @Column(name = "updated_at")
    private LocalDate updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDate.now();
        updatedAt = LocalDate.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDate.now();
    }
}

