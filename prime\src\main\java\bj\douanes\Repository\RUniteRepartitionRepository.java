package bj.douanes.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.RUniteRepartition;
import bj.douanes.Model.RUniteRepartitionId;

@Repository
public interface RUniteRepartitionRepository extends JpaRepository<RUniteRepartition, RUniteRepartitionId> {

    // List<RUniteRepartition> findByUnite_CodeUnite(String codeUnite);

    // List<RUniteRepartition> findByRepartition_IdRepartition(Integer idRepartition);

    @Modifying
    @Query("DELETE FROM RUniteRepartition ru WHERE ru.id.idRepartition = :idRepartition")
    void deleteByRepartitionId(Long idRepartition);
}
