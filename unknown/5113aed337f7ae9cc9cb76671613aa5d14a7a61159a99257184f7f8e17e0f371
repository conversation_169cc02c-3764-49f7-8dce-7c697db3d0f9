package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.PrimeFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/prime")
public class PrimeController {
    
    @Autowired
    private PrimeFacade primeFacade;

    @GetMapping
    public ResponseEntity<?> getAllPrime() {
        return AppResponse.ok(primeFacade.getPrimefAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getPrimeById(@PathVariable Long id) {
        return AppResponse.ok(primeFacade.getPrimeById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> addCertif(@RequestBody DTO.PrimeDto prime, @PathVariable Long agentId) {
        return AppResponse.created(primeFacade.createPrime(agentId, prime));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateCertif(@RequestBody DTO.PrimeDto prime,@PathVariable Long id){
        return AppResponse.ok(primeFacade.update(id, prime));
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteAgent(@PathVariable Long id){
        return AppResponse.ok(primeFacade.deletePrime(id)); 
    }
}
