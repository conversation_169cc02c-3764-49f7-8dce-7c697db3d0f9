package bj.douanes.core.config.database;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.core.env.Environment;

@Component
@RequiredArgsConstructor
@Slf4j
public class DatabaseComponent {

    private final Environment env;

    public String getPackage(String sourceType) {
        return this.env.getProperty(String.format("app.datasource.%s.packages", sourceType));
    }

    public String getEnv(String sourceType, String sourceName) {
        return this.env.getProperty(String.format("app.datasource.%s.%s", sourceType, sourceName));
    }

    public List<String> getEnvTypes() {
        return java.util.Arrays.asList(getListType().split(","));
    }

    public String getFirstType() {
        return getListType().split(",")[0];
    }

    public String getOrDefaultType(String type) {
        var listType = getListType();
        var lowerType = type.toLowerCase();
        return listType.contains(lowerType) ? lowerType : listType.split(",")[0];
    }

    private String getListType() {
        var listType = this.env.getProperty("app.datasources.typeNames", "").trim();
        if (listType.isEmpty()) {
            log.error(
                    "Configuration manquante : la propriete 'app.datasources.typeNames' est vide ou non définie. L'application ne peut pas demarrer sans cette configuration.");
            System.exit(1);
        }
        return listType.toLowerCase();
    }
}
