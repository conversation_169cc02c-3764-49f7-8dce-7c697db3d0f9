package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.LangueFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/langues")
public class LangueController {

    @Autowired
    private LangueFacade langueFacade;
    
    @GetMapping
    public ResponseEntity<?> getAllLangues() {
        return AppResponse.ok(langueFacade.getLanguesAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getLangueById(@PathVariable Long id) {
        return AppResponse.ok(langueFacade.getLanguesById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createLangue(@RequestBody DTO.LangueDto langueDto, @PathVariable Long agentId) {
        return AppResponse.created(langueFacade.createLangues(agentId, langueDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatedLangue(@RequestBody DTO.LangueDto langueDto,@PathVariable Long id){
        return AppResponse.ok(langueFacade.update(id, langueDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteNote(@PathVariable Long id){
        return AppResponse.ok(langueFacade.deleteLangues(id)); 
    }

}
