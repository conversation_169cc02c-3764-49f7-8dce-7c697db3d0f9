package bj.douanes.facade.Prime;
import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.DTO.MontantCollectParUniteDto;
import bj.douanes.Model.Repartition;
import bj.douanes.Services.RepartitionServ;
import bj.douanes.facade.UTILS.DTO.RepartitionDto;
import lombok.RequiredArgsConstructor;


public interface RepartitionFacade {
    Repartition createRepartition(Object repartition,List<MontantCollectParUniteDto> dtoList);
    Repartition getRepartitionById(Long id);
    List<Repartition> getAllRepartitions();
    Repartition updateRepartition(Long id, Object repartition);
    void deleteRepartition(Long id);
}

@Service
@RequiredArgsConstructor
class InnerRepartitionFacade implements RepartitionFacade {

    private final RepartitionServ repartitionServ;
    private final ModelMapper modelMapper;

    @Override
    public Repartition createRepartition(Object repartition,List<MontantCollectParUniteDto> dtoList) {
        RepartitionDto dto = (RepartitionDto) repartition;

            Repartition newRepartition = new Repartition();
            newRepartition.setCodeTypePrime(dto.getCodeTypePrime());
            //newRepartition.setIdPrime(dto.getId_prime()); 
            newRepartition.setPeriode(dto.getPeriode());
            newRepartition.setAnnee(dto.getAnnee());
        return repartitionServ.createRepartition(newRepartition, dtoList);
    }

    @Override
    public Repartition getRepartitionById(Long id) {
        return repartitionServ.getRepartitionById(id);
    }
    @Override
    public List<Repartition> getAllRepartitions() {
        return repartitionServ.getAllRepartitions();
    }
    @Override
    public Repartition updateRepartition(Long id, Object repartition) {
        var repartitionToUpdate = modelMapper.map(repartition, Repartition.class);
        return repartitionServ.updateRepartition(id, repartitionToUpdate);
    }
    @Override
    public void deleteRepartition(Long id) {
        repartitionServ.deleteRepartition(id);
    }
}
