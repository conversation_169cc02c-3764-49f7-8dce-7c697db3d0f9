package bj.douanes.core.auth.service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import bj.douanes.core.auth.model.Role;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.repository.RoleRepository;
import bj.douanes.core.auth.repository.UserRepository;
import bj.douanes.core.shared.error.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface CrudService {

     /**
     * Récupère tous les Utilisateurs
     */
    public List<User> findAllUsers();

    /**
     * Trouve un utilisateur par son identifiant
     */
    public User findUserById(UUID id);

    /**
     * Trouve un utilisateur par son email
     */
    public User findUserByEmail(String email);
    
    /**
     * Met à jour un utilisateur existant
     * Assurer vous que les informations sensibles ne sont pas modifiées
     */
    public User updateUser(UUID id, User user);

    /**
     * Supprime un utilisateur
     */
    public void deleteUser(UUID id);
    
    /**
     * Récupère tous les roles
     */
    List<Role> findAllRoles();
    
    /**
     * Trouve un role par son nom
     */
    Optional<Role> findRoleByName(String name);
    
    /**
     * Crée un nouveau role
     */
    Role createRole(String name, String description);
    
    /**
     * Met à jour un role existant
     */
    Role updateRole(String name, String newDescription);
    
    /**
     * Supprime un role
     */
    void deleteRole(String name);
    
    /**
     * Assigne un role à un utilisateur
     */
    void assignRoleToUser(UUID userId, String roleName);

    /**
     * Retire un role d'un utilisateur
     */
    void removeRoleFromUser(UUID userId, String roleName);

    /**
     * Récupère tous les roles d'un utilisateur
     */
    Set<Role> getUserRoles(UUID userId);

    /**
     * Vérifie si un utilisateur a un role spécifique
     */
    boolean userHasRole(UUID userId, String roleName);
    
    /**
     * Crée les roles par défaut s'ils n'existent pas
     */
    void initializeDefaultRoles();
}

@Slf4j
@Service
@RequiredArgsConstructor
class CrudServiceImpl implements CrudService {
    
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final ModelMapper modelMapper;    

    @Override
    public List<User> findAllUsers() {
        log.debug("Récupération de tous les utilisateurs");
        return userRepository.findAll();
    }

    @Override
    public User findUserById(UUID id) {
        log.debug("Recherche de l'utilisateur avec l'ID {}", id);
        return userRepository.findById(id)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + id));
    }

    @Override
    public User findUserByEmail(String email) {
        log.debug("Recherche de l'utilisateur avec l'email {}", email);
        return userRepository.findByEmailIgnoreCase(email)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'email: " + email));
    }

    @Override
    @Transactional
    public User updateUser(UUID id, User user) {
        // Vérifier si l'utilisateur existe
        User existing = userRepository.findById(id)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + id));

        log.info("Mise à jour de l'utilisateur: {}", existing.getEmail());
        // Mettre à jour les champs nécessaires avec ModelMapper
        modelMapper.map(user, existing);
        return userRepository.save(existing);
    }

    @Override
    @Transactional
    public void deleteUser(UUID id) {
        // Vérifier si l'utilisateur existe
        User user = userRepository.findById(id)
        .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + id));
        log.info("Suppression de l'utilisateur: {}", user.getEmail());
        userRepository.delete(user);
    }
    
    @Override
    public List<Role> findAllRoles() {
        log.debug("Recuperation de tous les roles");
        return roleRepository.findAll();
    }
    
    @Override
    public Optional<Role> findRoleByName(String name) {
        log.debug("Recherche du role: {}", name);
        return roleRepository.findByName(name);
    }
    
    @Override
    @Transactional
    public Role createRole(String name, String description) {
        log.info("Creation du role: {} - {}", name, description);
        
        if (roleRepository.existsByName(name)) {
            throw new ApiException("Le role '" + name + "' existe déjà");
        }
        
        Role role = Role.builder()
                .name(name.toUpperCase())
                .description(description)
                .build();
        
        return roleRepository.save(role);
    }
    
    @Override
    @Transactional
    public Role updateRole(String name, String newDescription) {
        log.info("Mise a jour du role: {}", name);
        
        Role role = roleRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Rôle '" + name + "' non trouvé"));
        
        role.setDescription(newDescription);
        return roleRepository.save(role);
    }
    
    @Override
    @Transactional
    public void deleteRole(String name) {
        log.info("Suppression du role: {}", name);
        
        Role role = roleRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Rôle '" + name + "' non trouvé"));
        
        // Vérifier si le role est utilisé par des utilisateurs
        Long userCount = roleRepository.countUsersByRoleName(name);
        if (userCount > 0) {
            throw new ApiException("Impossible de supprimer le rôle '" + name + 
                    "'. Il est utilisé par " + userCount + " utilisateur(s)");
        }
        
        roleRepository.delete(role);
    }
    
    @Override
    @Transactional
    public void assignRoleToUser(UUID userId, String roleName) {
        log.info("Attribution du role '{}' a l'utilisateur {}", roleName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new ApiException("Rôle '" + roleName + "' non trouvé"));

        if (user.hasRole(roleName)) {
            log.warn("L'utilisateur {} a deja le role '{}'", userId, roleName);
            return;
        }

        // Remplacer le rôle existant par le nouveau (un utilisateur = un rôle)
        if (user.haveRole()) {
            log.info("Remplacement du rôle '{}' par '{}' pour l'utilisateur {}",
                    user.getRoleName(), roleName, userId);
        }

        user.setRole(role);
        userRepository.save(user);
    }
    
    @Override
    @Transactional
    public void removeRoleFromUser(UUID userId, String roleName) {
        log.info("Retrait du role '{}' de l'utilisateur {}", roleName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        if (!user.hasRole(roleName)) {
            log.warn("L'utilisateur {} n'a pas le role '{}'", userId, roleName);
            return;
        }

        // Retirer le rôle (l'utilisateur n'aura plus de rôle)
        user.removeRole();
        userRepository.save(user);

        log.info("Rôle '{}' retiré de l'utilisateur {}", roleName, userId);
    }
    
    @Override
    public Set<Role> getUserRoles(UUID userId) {
        log.debug("Recuperation du role de l'utilisateur {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        // Retourner un Set avec le rôle unique de l'utilisateur (pour compatibilité)
        return user.haveRole() ? Set.of(user.getRole()) : Set.of();
    }
    
    @Override
    public boolean userHasRole(UUID userId, String roleName) {
        log.debug("Verification si l'utilisateur {} a le role '{}'", userId, roleName);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));
        
        return user.hasRole(roleName);
    }
    
    @Override
    @Transactional
    public void initializeDefaultRoles() {
        log.info("Initialisation des roles par defaut si elles n'existent pas...");        
        // Créer le role de base s'ils n'existent pas
        createRoleIfNotExists("ADMIN", "Administrateur avec tous les privilèges");
        createRoleIfNotExists("USER", "Utilisateur standard avec accès de base");
    }
    
    private void createRoleIfNotExists(String name, String description) {
        if (!roleRepository.existsByName(name)) {
            Role role = Role.builder()
                    .name(name)
                    .description(description)
                    .build();
            roleRepository.save(role);
            log.info("Role '{}' cree avec succes", name);
        }
    }
}
