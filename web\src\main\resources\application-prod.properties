server.port=80

app.datasources.typeNames=primary

#Primary DB
#postgres database configuration
app.datasource.primary.url=***************************************
app.datasource.primary.username=root
app.datasource.primary.password=rootoor
app.datasource.primary.driver-class-name=org.postgresql.Driver
app.datasource.primary.dialect=org.hibernate.dialect.PostgreSQLDialect
app.datasource.primary.packages=bj.douanes.personal,bj.douanes.transport,bj.douanes.sydoniaplusplus,bj.douanes.core,bj.douanes.prime
# app.datasource.primary.url=**************************************
# app.datasource.primary.username=root
# app.datasource.primary.password=root
# app.datasource.primary.driver-class-name=org.postgresql.Driver


app.jwt.secret-key=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898E
app.jwt.delay=24

app.security.white-list=/,/*.*,/static/**,/assets/**,/eservice/**,/h2-console/**,/v3/api-docs/**,/swagger-ui/**,\
                        /api/auth/**,/api/asy/**,\
                        /api/agents/**,/api/admin/**,/api/transport/**,/api/prime/**

#----------------------------------------------------------------------------------------------
spring.mail.host=mail.finances.bj
spring.mail.properties.mail.smtp.localhost=127.0.0.1
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=BJDgD2022_
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.debug=true

# Configuration pour l'envoi d'emails
#spring.mail.host=sandbox.smtp.mailtrap.io
# spring.mail.port=587
# spring.mail.username=7d17f7f41a081f
# spring.mail.password=93800895c5d0c4
# spring.mail.properties.mail.smtp.auth=plain
# spring.mail.properties.mail.smtp.starttls.enable=true
# spring.mail.properties.mail.debug=true