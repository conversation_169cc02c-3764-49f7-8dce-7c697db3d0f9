package bj.douanes.core.auth.repository;

import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import bj.douanes.core.auth.model.User;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {
    /**
     * Trouve un utilisateur par son email, insensible à la casse.
     *
     * @param email l'email de l'utilisateur
     * @return l'utilisateur si trouvé, null sinon
     */    
    Optional<User> findByEmailIgnoreCase(String email);

    /**
     * Vérifie si un utilisateur existe par son email, insensible à la casse.
     *
     * @param email l'email de l'utilisateur
     * @return true si l'utilisateur existe, false sinon
     */
    public boolean existsByEmailIgnoreCase(String email);
}
