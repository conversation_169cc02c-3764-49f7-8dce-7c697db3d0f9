package bj.douanes.facade.Prime;

import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.DTO.FonctionsDTO;
import bj.douanes.Model.RFonctionTypePrime;
import bj.douanes.Services.FonctionServ;
import lombok.RequiredArgsConstructor;

public interface FonctionFacade {
    
    List<FonctionsDTO> getAllFonctions();
    FonctionsDTO getFonctionById(String codeFonction);
    FonctionsDTO createFonction(FonctionsDTO fonctionDTO);
    FonctionsDTO updateFonction(String codeFonction, FonctionsDTO fonctionDTO);
    void deleteFonction(String codeFonction);
}

@Service
@RequiredArgsConstructor
class FonctionFacadeImpl implements FonctionFacade {

    private final FonctionServ fonctionServ;
    private final ModelMapper modelMapper;


    @Override
    public List<FonctionsDTO> getAllFonctions() {
        return fonctionServ.getAllFonctions();
    }

    @Override
    public FonctionsDTO getFonctionById(String codeFonction) {
        return fonctionServ.getAllFonctions().stream()
                .filter(fonction -> fonction.getCodeFonction().equals(codeFonction))
                .findFirst()
                .orElse(null);
    }

    @Override
    public FonctionsDTO createFonction(FonctionsDTO fonctionDTO) {
        RFonctionTypePrime rftp = fonctionServ.createFonction(fonctionDTO);
        return modelMapper.map(rftp, FonctionsDTO.class);      
    }

    @Override
    public FonctionsDTO updateFonction(String codeFonction, FonctionsDTO fonctionDTO) {
        return fonctionServ.updateFonction(codeFonction, fonctionDTO);
    }

    @Override
    public void deleteFonction(String codeFonction) {
        fonctionServ.deleteFonctionForTSD(codeFonction);
    }
}