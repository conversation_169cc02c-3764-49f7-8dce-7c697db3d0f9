package bj.douanes.DTO;

import java.math.BigDecimal;
import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgentCreationDto {
    
    private String matricule;
    private String nom;
    private String prenoms;
    private String sexe; // 'M' ou 'F'
    private LocalDate dateNais;
    private String ifu;
    private String telephone;
    private String email;
    private Boolean actif = true;

    // Rattachements aux unités (r_agent_unite)
    private AgentUniteDto unites;

    // Rattachements aux fonctions (r_agent_fonction)
    private AgentFonctionDto fonctions;

    // Informations bancaires (r_agent_banque)
    private AgentBanqueDto banques;

    private PrimeTsdAgentDto primeTsdAgent; 

    @Data
    public static class AgentUniteDto {
        private String codeUnite;
        private LocalDate dateDebut;
        private LocalDate dateFin;
    }

    @Data
    public static class AgentFonctionDto {
        private String codeFonction;
        private LocalDate dateDebut;
        private LocalDate dateFin;
    }

    @Data
    public static class AgentBanqueDto {
        private String codeBanque;
        private String rib;
        private LocalDate dateDebut;
        private LocalDate dateFin;

        private BanqueDto banque;  
    }

    @Data
    public static class BanqueDto {
        private String codeBanque;
        private String nomBanque;
    }

    @Data
    public static class PrimeTsdAgentDto {
        private BigDecimal montantBonification = BigDecimal.ZERO;
        private BigDecimal partGlobalReparti = BigDecimal.ZERO;
        private BigDecimal totalBrut = BigDecimal.ZERO;
        private BigDecimal totalArrondi = BigDecimal.ZERO;
        private BigDecimal montantIts = BigDecimal.ZERO;
        private BigDecimal itsArrondi = BigDecimal.ZERO;
        private BigDecimal montantNetPayer = BigDecimal.ZERO;

        private String matricule;
        private Long idRepartition;
    }
}