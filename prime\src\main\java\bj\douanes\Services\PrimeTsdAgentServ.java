package bj.douanes.Services;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.PrimeTSDUniteDto;
import bj.douanes.DTO.PrimeTsdAgentDto;
import bj.douanes.Model.PrimeTSD;
import bj.douanes.Model.PrimeTsdAgent;
import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.RAgentRepartitionId;
import bj.douanes.Repository.PrimeTSDRepo;
import bj.douanes.Repository.PrimeTsdAgentRepository;
import bj.douanes.Repository.PrimeTsdUniteRepository;
import bj.douanes.Repository.RAgentRepartitionRepository;
import bj.douanes.Repository.RAgentUniteRepository;
import bj.douanes.Repository.RFonctionTypePrimeRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class PrimeTsdAgentServ {

    private final PrimeTsdAgentRepository primeTsdAgentRepository;
    private final RFonctionTypePrimeRepository rFonctionTypePrimeRepository;
    private final RAgentUniteRepository rAgentUniteRepository;
    private final PrimeTsdUniteRepository primeTsdUniteRepository;
    private final PrimeTSDRepo primeTSDRepository;
    private final RAgentRepartitionRepository rAgentRepartitionRepository;

    @Transactional
    public List<PrimeTsdAgentDto> createPrimeTsdAgent(Long idRepartition) {

        List<RAgentRepartition> rAgentRepartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);
        List<PrimeTsdAgent> createdAgents = new ArrayList<>(); // Liste pour stocker les nouveaux agents

        for (RAgentRepartition rAgentRepartition : rAgentRepartitions) {
            //recuperer le matricule
            RAgentRepartitionId id = rAgentRepartition.getId();
            String matricule = id.getMatricule();
            String codeUnite = rAgentUniteRepository.findCodeUniteByMatricule(matricule);
            System.out.println("Code Unite Agent: " + codeUnite + ", Matricule: " + matricule);
            
            PrimeTsdAgent primeTsdAgent = new PrimeTsdAgent();

            // Récupérer les informations de PrimeTSDUnite
            PrimeTSDUniteDto primeTSDUniteDto = primeTsdUniteRepository.findByIdRepartitionAndCodeUnite(idRepartition, codeUnite);
            System.out.println("PrimeTSDUniteDto: " + primeTSDUniteDto);

            BigDecimal montantBonif = primeTSDUniteDto != null ? primeTSDUniteDto.getBonificationUnite(): BigDecimal.ZERO;

            BigDecimal cumulCoef = rFonctionTypePrimeRepository.getCumulCoefParUniteTSDDesAgentsActifs(codeUnite);
            System.out.println("Cumul Coef: " + cumulCoef);

            BigDecimal coefAgent = rFonctionTypePrimeRepository.getCoefficientByMatriculeAndTypePrime(matricule);
            System.out.println("Coefficient Agent: " + coefAgent);

            // Calcul du montant de bonification
            BigDecimal montantBonification = BigDecimal.ZERO;
            if (cumulCoef.compareTo(BigDecimal.ZERO) > 0) {
                montantBonification = coefAgent.multiply(montantBonif).divide(cumulCoef, 2, RoundingMode.HALF_UP);
            }

            PrimeTSD primeTSD = primeTSDRepository.findByRepartitionIdRepartition(idRepartition);
            if (primeTSD == null) {
                continue;
            }

            BigDecimal montantGlobal = primeTSD.getPartGlobal();
            System.out.println("Montant Global: " + montantGlobal);
            BigDecimal cumulCoefficient = primeTSD.getCumulCoefGlobal();
            System.out.println("Cumul Coefficient Global: " + cumulCoefficient);

            BigDecimal partGlobalAgent = BigDecimal.ZERO;
            if (cumulCoefficient.compareTo(BigDecimal.ZERO) > 0) {//verifier si le denominateur est > a 0
                partGlobalAgent = montantGlobal.multiply(coefAgent).divide(cumulCoefficient, 2, RoundingMode.HALF_UP);
                System.out.println("Part Global Agent: " + partGlobalAgent);
            }

            BigDecimal totalBrut = partGlobalAgent.add(montantBonification);
            System.out.println("Total Brut: " + totalBrut);
            //afficher le matricule,codeUnite, montantBonification, partGlobalAgent, totalBrut
            System.out.println("====================>Matricule: " + matricule + ", Code Unite: " + codeUnite +
                    ", Montant Bonification: " + montantBonification +
                    ", Part Global Agent: " + partGlobalAgent +
                    ", Total Brut: " + totalBrut+" <====================");

            // Remplir les champs de PrimeTsdAgent
            primeTsdAgent.setMontantBonification(montantBonification);
            primeTsdAgent.setPartGlobalReparti(partGlobalAgent);
            primeTsdAgent.setTotalBrut(totalBrut);

            //CALCUL DE TOTAL ARRONDI EN FAISANT (TOTAL BRUT/1000)*1000
            BigDecimal totalArrondi = totalBrut.divide(BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(1000));
            System.out.println("Total Arrondi: " + totalArrondi);
            primeTsdAgent.setTotalArrondi(totalArrondi);

            //CALCUL DU MONTANT ITS SELON =SI(totalArrondi<=60000;0;SI(totalArrondi<=150000;(totalArrondi-60000)*10%;SI(totalArrondi<=250000;9000+(totalArrondi-150000)*15%;SI(totalArrondi<=500000;24000+(totalArrondi-250000)*19%;71500+(totalArrondi-500000)*30%))))
            BigDecimal montantIts = BigDecimal.ZERO;
            if (totalArrondi.compareTo(BigDecimal.valueOf(60000)) <= 0) {
                montantIts = BigDecimal.ZERO;
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(150000)) <= 0) {
                montantIts = totalArrondi.subtract(BigDecimal.valueOf(60000)).multiply(BigDecimal.valueOf(0.10));
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(250000)) <= 0) {
                montantIts = BigDecimal.valueOf(9000).add(totalArrondi.subtract(BigDecimal.valueOf(150000)).multiply(BigDecimal.valueOf(0.15)));
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(500000)) <= 0) {
                montantIts = BigDecimal.valueOf(24000).add(totalArrondi.subtract(BigDecimal.valueOf(250000)).multiply(BigDecimal.valueOf(0.19)));
            } else {
                montantIts = BigDecimal.valueOf(71500).add(totalArrondi.subtract(BigDecimal.valueOf(500000)).multiply(BigDecimal.valueOf(0.30)));
            }
            System.out.println("Montant ITS: " + montantIts);
            primeTsdAgent.setMontantIts(montantIts);
            
            //CALCUL DE L'ITS ARRONDI EN FAISANT ARRONDI(montantIts;-1)
            BigDecimal itsArrondi = montantIts.setScale(-1, RoundingMode.HALF_UP);
            System.out.println("ITS Arrondi: " + itsArrondi);
            primeTsdAgent.setItsArrondi(itsArrondi);

            //CALCUL DU MONTANT NET A PAYER EN FAISANT totalBrut - itsArrondi
            BigDecimal montantNetPayer = totalBrut.subtract(itsArrondi);
            //supprimer la patie décimale de montantNetPayer
            montantNetPayer = montantNetPayer.setScale(0, RoundingMode.DOWN);
            System.out.println("Montant Net à Payer: " + montantNetPayer);
            primeTsdAgent.setMontantNetPayer(montantNetPayer);
            // Afficher les informations de l'agent 
            System.out.println("--------------->Agent repartie: " + matricule + ", Montant Bonification: " + montantBonification +
                    ", Part Global Reparti: " + partGlobalAgent +
                    ", Total Brut: " + totalBrut +
                    ", Total Arrondi: " + totalArrondi +
                    ", Montant ITS: " + montantIts +
                    ", ITS Arrondi: " + itsArrondi +
                    ", Montant Net à Payer: " + montantNetPayer+" <---------------");

            // Charger RAgentRepartition existant depuis la base
            RAgentRepartitionId rAgentRepartitionId = new RAgentRepartitionId();
            rAgentRepartitionId.setMatricule(matricule);
            rAgentRepartitionId.setIdRepartition(idRepartition);

            Optional<RAgentRepartition> rAgentRepartitionOpt = rAgentRepartitionRepository.findById(rAgentRepartitionId);

            if (!rAgentRepartitionOpt.isPresent()) {
                continue;
            }

            primeTsdAgent.setRAgentRepartition(rAgentRepartitionOpt.get());

            PrimeTsdAgent saved = primeTsdAgentRepository.save(primeTsdAgent); // Sauvegarde
            rAgentRepartitionOpt.get().setIdPrimeAgent(saved.getIdPrimeAgent()); // Mettre à jour l'ID de l'agent dans RAgentRepartition
            rAgentRepartitionRepository.save(rAgentRepartitionOpt.get()); // ENREGISTREMENT
            createdAgents.add(saved); // Ajout à la liste des agents créés
        }

        // Mapping en DTOs
        List<PrimeTsdAgentDto> dtos = new ArrayList<>();
        for (PrimeTsdAgent agent : createdAgents) {
            dtos.add(enDto(agent));
        }
        return dtos;
        
    }
    // Méthode de mapping vers le DTO
    private PrimeTsdAgentDto enDto(PrimeTsdAgent agent) {
        PrimeTsdAgentDto dto = new PrimeTsdAgentDto();
        dto.setIdPrimeAgent(agent.getIdPrimeAgent());
        if (agent.getRAgentRepartition() != null && agent.getRAgentRepartition().getId() != null) {
            dto.setMatricule(agent.getRAgentRepartition().getId().getMatricule());
            dto.setIdRepartition(agent.getRAgentRepartition().getId().getIdRepartition());
        }
        dto.setMontantBonification(agent.getMontantBonification());
        dto.setPartGlobalReparti(agent.getPartGlobalReparti());
        dto.setTotalBrut(agent.getTotalBrut());
        dto.setTotalArrondi(agent.getTotalArrondi());
        dto.setMontantIts(agent.getMontantIts());
        dto.setItsArrondi(agent.getItsArrondi());
        dto.setMontantNetPayer(agent.getMontantNetPayer());
        return dto;
    }

}


