package bj.douanes.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.PrimeTSD;

@Repository
public interface PrimeTSDRepo extends JpaRepository<PrimeTSD, Long> {

    PrimeTSD findByRepartitionIdRepartition(Long idRepartition);

    @Modifying
    @Query("DELETE FROM PrimeTSD p WHERE p.repartition.idRepartition = :idRepartition")
    void deleteByRepartitionId(Long idRepartition);
    


}
