package bj.douanes.DTO;

import java.math.BigDecimal;
import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FonctionsDTO {
    
    private String codeFonction;
    private String libelle;
    private String codeTypePrime;
    private BigDecimal coefficient;
    private LocalDate dateDebut = LocalDate.now();
}
