package bj.douanes.core.config.database;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
@EnableJpaRepositories(basePackages = "bj.douanes", entityManagerFactoryRef = "entityManagerFactory", transactionManagerRef = "transactionManager")
public class DataSourceConfig {

    private static final String DEFAULT_DRIVER = "org.postgresql.Driver";
    private static final String DEFAULT_DIALECT = "org.hibernate.dialect.PostgreSQLDialect";

    private final DatabaseComponent dbComponent;

    private boolean isAlreadyBuild = false;
    private Map<Object, Object> dataSourcesBuild;

    @Bean
    @Primary
    public DataSource dataSource() {
        if (!isAlreadyBuild) {
            isAlreadyBuild = !isAlreadyBuild;
            dataSourcesBuild = this.buildDataSources();
        }

        final RoutingDataSource routingDataSource = new RoutingDataSource();
        routingDataSource.setTargetDataSources(dataSourcesBuild);
        routingDataSource.setDefaultTargetDataSource(dataSourcesBuild.get(dbComponent.getFirstType()));
        return routingDataSource;
    }

    @Bean
    @Primary
    public EntityManagerFactory entityManagerFactory() {
        if (!isAlreadyBuild) {
            isAlreadyBuild = !isAlreadyBuild;
            dataSourcesBuild = this.buildDataSources();
        }

        final Map<Object, EntityManagerFactory> entityManagerFactories = buildEntityManagerFactories(dataSourcesBuild);

        EntityManagerFactory defaultEmf = entityManagerFactories.get(dbComponent.getFirstType());
        return new RoutingEntityManagerFactory(entityManagerFactories, defaultEmf);
    }

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        return new JpaTransactionManager(entityManagerFactory());
    }

    private Map<Object, EntityManagerFactory> buildEntityManagerFactories(Map<Object, Object> dataSources) {
        final Map<Object, EntityManagerFactory> result = new HashMap<>();

        for (Map.Entry<Object, Object> entry : dataSources.entrySet()) {
            result.put(entry.getKey(), this.buildEntityManagerFactory(
                    (DataSource) entry.getValue(),
                    (String) entry.getKey()));
        }

        return result;
    }

    private EntityManagerFactory buildEntityManagerFactory(DataSource dataSource, String sourceType) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("bj.douanes");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        // Déterminer automatiquement le dialect depuis l'URL
        String dialectToUse = determineDialectFromUrl(this.dbComponent.getEnv(sourceType, "url"));

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.dialect", dialectToUse);

        // Configuration de la stratégie de naming par défaut : camelCase → snake_case
        properties.put("hibernate.physical_naming_strategy",
                "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
        properties.put("hibernate.implicit_naming_strategy",
                "org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl");

        em.setJpaPropertyMap(properties);

        em.afterPropertiesSet();
        return em.getObject();
    }

    private Map<Object, Object> buildDataSources() {
        final Map<Object, Object> result = new HashMap<>();

        for (String sourceType : dbComponent.getEnvTypes()) {
            result.put(sourceType, this.buildDataSource(sourceType));
        }

        return result;
    }

    private DataSource buildDataSource(String sourceType) {
        final HikariConfig config = new HikariConfig();
        final String URL = dbComponent.getEnv(sourceType, "url");
        config.setJdbcUrl(URL);
        config.setUsername(dbComponent.getEnv(sourceType, "username"));
        config.setPassword(dbComponent.getEnv(sourceType, "password"));
        // Déterminer automatiquement le driver depuis l'URL
        config.setDriverClassName(determineDriverFromUrl(URL));
        config.setAutoCommit(false);

        return new HikariDataSource(config);
    }

    /**
     * Détermine automatiquement le driver JDBC en fonction de l'URL JDBC
     *
     * @param jdbcUrl l'URL JDBC de la base de données
     * @return le nom de classe du driver JDBC approprié
     */
    private String determineDriverFromUrl(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.isEmpty()) {
            return DEFAULT_DRIVER; // Driver par défaut
        }

        String url = jdbcUrl.toLowerCase();

        if (url.startsWith("jdbc:postgresql:")) {
            return DEFAULT_DRIVER;
        } else if (url.startsWith("jdbc:mysql:")) {
            return "com.mysql.cj.jdbc.Driver";
        } else if (url.startsWith("jdbc:mariadb:")) {
            return "org.mariadb.jdbc.Driver";
        } else if (url.startsWith("jdbc:oracle:")) {
            return "oracle.jdbc.OracleDriver";
        } else if (url.startsWith("jdbc:sqlserver:")) {
            return "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        } else if (url.startsWith("jdbc:h2:")) {
            return "org.h2.Driver";
        } else if (url.startsWith("jdbc:sqlite:")) {
            return "org.sqlite.JDBC";
        } else if (url.startsWith("jdbc:hsqldb:")) {
            return "org.hsqldb.jdbc.JDBCDriver";
        } else if (url.startsWith("jdbc:derby:")) {
            return "org.apache.derby.jdbc.EmbeddedDriver";
        } else {
            // Driver par défaut si le type de base de données n'est pas reconnu
            return DEFAULT_DRIVER;
        }
    }

    /**
     * Détermine automatiquement le dialecte Hibernate en fonction de l'URL JDBC
     *
     * @param jdbcUrl l'URL JDBC de la base de données
     * @return le dialecte Hibernate approprié
     */
    private String determineDialectFromUrl(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.isEmpty()) {
            return DEFAULT_DIALECT; // Valeur par défaut
        }

        String url = jdbcUrl.toLowerCase();

        if (url.startsWith("jdbc:postgresql:")) {
            return DEFAULT_DIALECT;
        } else if (url.startsWith("jdbc:mysql:")) {
            return "org.hibernate.dialect.MySQLDialect";
        } else if (url.startsWith("jdbc:mariadb:")) {
            return "org.hibernate.dialect.MariaDBDialect";
        } else if (url.startsWith("jdbc:oracle:")) {
            return "org.hibernate.dialect.OracleDialect";
        } else if (url.startsWith("jdbc:sqlserver:")) {
            return "org.hibernate.dialect.SQLServerDialect";
        } else if (url.startsWith("jdbc:h2:")) {
            return "org.hibernate.dialect.H2Dialect";
        } else if (url.startsWith("jdbc:sqlite:")) {
            return "org.hibernate.dialect.SQLiteDialect";
        } else if (url.startsWith("jdbc:hsqldb:")) {
            return "org.hibernate.dialect.HSQLDialect";
        } else if (url.startsWith("jdbc:derby:")) {
            return "org.hibernate.dialect.DerbyDialect";
        } else {
            // Valeur par défaut si le type de base de données n'est pas reconnu
            return DEFAULT_DIALECT;
        }
    }
}