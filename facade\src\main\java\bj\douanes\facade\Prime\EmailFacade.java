package bj.douanes.facade.Prime;

import org.springframework.stereotype.Service;

import bj.douanes.Services.EmailService;
import lombok.RequiredArgsConstructor;

public interface EmailFacade {
    void sendOrdreVirementEmailToAllAgents(Long idRepartition);
}

@Service
@RequiredArgsConstructor
class InnerEmailFacade implements EmailFacade {

    private final EmailService emailService;


    @Override
    public void sendOrdreVirementEmailToAllAgents(Long idRepartition) {
        try {
            emailService.sendOrdreVirementEmailToAllAgents(idRepartition);
        } catch (Exception e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }
}
