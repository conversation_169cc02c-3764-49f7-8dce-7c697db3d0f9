package bj.douanes.Model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "primetsd_agent")
public class PrimeTsdAgent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_prime_agent")
    private Long idPrimeAgent;

    @Column(name = "montant_bonification")
    private BigDecimal montantBonification = BigDecimal.ZERO;

    @Column(name = "part_global_reparti")
    private BigDecimal partGlobalReparti = BigDecimal.ZERO;

    @Column(name = "total_brut")
    private BigDecimal totalBrut = BigDecimal.ZERO;

    @Column(name = "total_arrondi")
    private BigDecimal totalArrondi = BigDecimal.ZERO;

    @Column(name = "montant_its")
    private BigDecimal montantIts = BigDecimal.ZERO;

    @Column(name = "its_arrondi")
    private BigDecimal itsArrondi = BigDecimal.ZERO;

    @Column(name = "montant_netpayer")
    private BigDecimal montantNetPayer = BigDecimal.ZERO;

    @Column(name = "created_at", nullable = false)
    private LocalDate createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDate updatedAt;

    @ManyToOne
    @JoinColumns({
        @JoinColumn(name = "matricule", referencedColumnName = "matricule"),
        @JoinColumn(name = "id_repartition", referencedColumnName = "id_repartition")
    })
    @JsonIgnore
    private RAgentRepartition rAgentRepartition;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDate.now();
        updatedAt = LocalDate.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDate.now();
    }
}

