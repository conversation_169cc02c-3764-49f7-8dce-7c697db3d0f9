# Exemple : Ajout d'un Nouveau Module

## Scénario

Supposons que vous voulez ajouter un nouveau module `inventory` à votre application.

## 1. Structure du Module

```
inventory/
├── src/main/java/bj/douanes/inventory/
│   ├── service/
│   │   ├── InventoryService.java
│   │   └── StockService.java
│   ├── model/
│   │   └── Product.java
│   └── repository/
│       └── ProductRepository.java
└── pom.xml
```

## 2. Service Example

```java
package bj.douanes.inventory.service;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class InventoryService {
    
    // Cette méthode sera automatiquement interceptée par l'aspect
    // grâce au pattern bj.douanes.*.service.*
    public void createProduct(Product product) {
        log.info("Création d'un produit dans l'inventaire");
        // La datasource sera déterminée automatiquement selon la configuration
    }
    
    public void updateStock(Long productId, Integer quantity) {
        log.info("Mise à jour du stock pour le produit {}", productId);
        // Même datasource que createProduct automatiquement
    }
}
```

## 3. Configuration

### Option A : Utiliser la datasource par défaut

Aucune configuration supplémentaire nécessaire. Le module utilisera `default-data-source=primary`.

### Option B : Datasource dédiée

```properties
# Nouvelle datasource pour l'inventaire
app.datasource.type.names=primary,inventory_db

# Configuration de la datasource inventory
app.datasource.inventory_db.url=**********************************************
app.datasource.inventory_db.username=postgres
app.datasource.inventory_db.password=password
app.datasource.inventory_db.driver-class-name=org.postgresql.Driver

# Mapping du package vers la datasource
app.datasource.mapping.packages.bj.douanes.inventory=inventory_db
```

## 4. Résultat

✅ **Automatique** : Tous les services du module `inventory` utiliseront automatiquement la datasource `inventory_db`

✅ **Aucun code supplémentaire** : Pas besoin de modifier l'aspect ou d'ajouter des annotations

✅ **Logs automatiques** :
```
DEBUG ServiceExecutionDatabaseRouterAspect - Routage automatique: bj.douanes.inventory.service.InventoryService -> datasource 'inventory_db'
```

## 5. Test

```java
@RestController
@RequestMapping("/api/inventory")
public class InventoryController {
    
    @Autowired
    private InventoryService inventoryService;
    
    @PostMapping("/products")
    public String createProduct(@RequestBody Product product) {
        inventoryService.createProduct(product);
        return "Produit créé avec la datasource inventory_db";
    }
}
```

## Avantages de cette Approche

1. **Zero Configuration** : Le nouveau module fonctionne immédiatement
2. **Pattern Matching** : `bj.douanes.*.service.*` capture automatiquement le nouveau module
3. **Flexibilité** : Possibilité d'override avec `@WithDatabase` si nécessaire
4. **Maintenabilité** : Pas besoin de modifier le code existant

Cette approche rend l'ajout de nouveaux modules extrêmement simple et maintient la cohérence du routage des datasources dans toute l'application.
