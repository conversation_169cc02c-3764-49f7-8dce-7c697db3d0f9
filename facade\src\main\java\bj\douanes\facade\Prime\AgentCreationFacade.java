package bj.douanes.facade.Prime;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.AgentCreationDto;
import bj.douanes.DTO.AgentCreationDto2;
import bj.douanes.DTO.AgentDto2;
import bj.douanes.Services.AgentCreationService;
import lombok.RequiredArgsConstructor;

public interface AgentCreationFacade {

    List<AgentCreationDto2> getAllAgents();
    List<AgentDto2> findListAllAgents(Long idRepartition);
    // List<AgentDto> getAllAgentsByUnite(Long idRepartition);
    void createAgent(AgentCreationDto agentDetails);
    void updateAgent(String matricule, AgentCreationDto agentDetails);
    byte[] downloadAgentList(Long idRepartition) throws IOException;
    byte[] downloadOrdreVirementList(Long idRepartition) throws IOException;
    
}

@Service
@RequiredArgsConstructor
class InnerAgentCreationFacade implements AgentCreationFacade {

    private final AgentCreationService agentCreationService;

    @Override
    public List<AgentDto2> findListAllAgents(Long idRepartition) {
        return agentCreationService.getListAllAgentsRepart(idRepartition);
    }

    // @Override
    // public List<AgentDto> getAllAgentsByUnite(Long idRepartition) {
    //     return agentCreationService.getAllAgentDoRepartPrime(idRepartition);
    // }

    @Override
    public void createAgent(AgentCreationDto agentDetails) {
        agentCreationService.createAgentFromDto(agentDetails);
    }

    @Override
    public void updateAgent(String matricule, AgentCreationDto agentDetails) {
         agentCreationService.updateAgentFromDto(matricule, agentDetails);
    }
    
    @Override
    public byte[] downloadAgentList(Long idRepartition) throws IOException{
        return agentCreationService.downloadAgentsExcel(idRepartition);
    }

    @Override
    public byte[] downloadOrdreVirementList(Long idRepartition) throws IOException {
        return agentCreationService.downloadOrdreVirementExcel(idRepartition);
    }

    @Override
    public List<AgentCreationDto2> getAllAgents() {
        return agentCreationService.getListAgents();
    }

    
}