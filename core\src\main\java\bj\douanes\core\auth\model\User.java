package bj.douanes.core.auth.model;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.ToString;

@Data
@Builder
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"role", "permissions"})
@ToString(exclude = {"role", "permissions"})
@Entity
@Table(name = "utilisateurs")
public class User implements UserDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    private String email;
    private String password;

    // Un utilisateur a exactement un rôle (relation ManyToOne)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "role_name", referencedColumnName = "name")
    private Role role;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "user_permissions",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    @Builder.Default
    private Set<Permission> permissions = new HashSet<>();

    private String matricule;
    private boolean isSuspended;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public User() {
        this.permissions = new HashSet<>();
        this.isSuspended = false;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        Set<GrantedAuthority> authorities = new HashSet<>();

        // Ajouter le rôle avec le préfixe "ROLE_" (un seul rôle)
        if (haveRole()) {
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getName()));

            // Ajouter les permissions par défaut du rôle
            role.getDefaultPermissions().stream()
                .map(permission -> new SimpleGrantedAuthority(permission.getName()))
                .forEach(authorities::add);
        }

        // Ajouter les permissions directes de l'utilisateur
        permissions.stream()
            .map(permission -> new SimpleGrantedAuthority(permission.getName()))
            .forEach(authorities::add);

        return authorities;
    }

    @Override
    public String getUsername() {
        return email;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return !this.isSuspended;
    }

    // Méthodes utilitaires pour la gestion du rôle (un seul rôle)
    public void setRole(Role role) {
        this.role = role;
    }

    public void removeRole() {
        this.role = null;
    }

    public boolean haveRole() {
        return role != null;
    }

    public boolean hasRole(String roleName) {
        return haveRole() && role.getName().equals(roleName);
    }

    public Role getRole() {
        return haveRole() ? role : null;
    }

    public String getRoleName() {
        return haveRole() ? role.getName() : null;
    }

    // Méthodes utilitaires pour la gestion des permissions
    public void addPermission(Permission permission) {
        this.permissions.add(permission);
    }

    public void removePermission(Permission permission) {
        this.permissions.remove(permission);
    }

    public boolean hasPermission(String permissionName) {
        return permissions.stream()
                .anyMatch(permission -> permission.getName().equals(permissionName));
    }

    public Set<String> getPermissionNames() {
        return permissions.stream()
                .map(Permission::getName)
                .collect(java.util.stream.Collectors.toSet());
    }

    public Set<String> getAllPermissionNames() {
        Set<String> allPermissions = new HashSet<>();

        // Permissions directes
        allPermissions.addAll(getPermissionNames());

        // Permissions du rôle (un seul rôle)
        if (haveRole()) {
            role.getDefaultPermissions().stream()
                .map(Permission::getName)
                .forEach(allPermissions::add);
        }

        return allPermissions;
    }

}
