package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.TypePrimeFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/typeprime")
public class TypePrimeController {

    private final TypePrimeFacade typePrimeFacade;

    @GetMapping("/primes")
    public ResponseEntity<?> getAllPrimes() {
        return AppResponse.ok(typePrimeFacade.getAllPrimes());
    }

    @GetMapping("/primes/{id}")
    public ResponseEntity<?> getPrimeById(@PathVariable String id) {
        return AppResponse.ok(typePrimeFacade.getPrimeById(id));
    }
    @PostMapping("/createTypePrime")
    public ResponseEntity<?> createPrime(@RequestBody DTO.TypePrimeDto primeDto) {
        return AppResponse.created(typePrimeFacade.createPrime(primeDto));
    }
    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatePrime(@RequestBody DTO.TypePrimeDto primeDto, @PathVariable String id) {
        return AppResponse.ok(typePrimeFacade.updatePrime(id, primeDto));
    }
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deletePrime(@PathVariable String id) {
        typePrimeFacade.deletePrime(id);
        return AppResponse.ok("TypePrime deleted successfully");
    }
   
}
