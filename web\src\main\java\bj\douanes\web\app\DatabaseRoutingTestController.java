package bj.douanes.web.app;

import bj.douanes.core.service.DatabaseRoutingTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Contrôleur de test pour démontrer le routage automatique des datasources
 */
@Slf4j
@RestController
@RequestMapping("/api/test/database-routing")
@RequiredArgsConstructor
public class DatabaseRoutingTestController {

    private final DatabaseRoutingTestService coreTestService;

    /**
     * Test du routage automatique
     * GET /api/test/database-routing/auto
     */
    @GetMapping("/auto")
    public String testAutomaticRouting() {
        log.info("🧪 Début du test de routage automatique");
        coreTestService.testCoreServiceRouting();
        return "Test de routage automatique terminé - vérifiez les logs pour voir quelle datasource a été utilisée";
    }

    /**
     * Test du routage explicite
     * GET /api/test/database-routing/explicit
     */
    @GetMapping("/explicit")
    public String testExplicitRouting() {
        log.info("🧪 Début du test de routage explicite");
        coreTestService.testExplicitRouting();
        return "Test de routage explicite terminé - vérifiez les logs pour voir quelle datasource a été utilisée";
    }

    /**
     * Test des deux types de routage
     * GET /api/test/database-routing/both
     */
    @GetMapping("/both")
    public String testBothRoutingTypes() {
        log.info("🧪 Début du test des deux types de routage");

        log.info("--- Test 1: Routage automatique ---");
        coreTestService.testCoreServiceRouting();

        log.info("--- Test 2: Routage explicite ---");
        coreTestService.testExplicitRouting();

        return "Tests de routage terminés - vérifiez les logs pour voir les datasources utilisées";
    }
}
