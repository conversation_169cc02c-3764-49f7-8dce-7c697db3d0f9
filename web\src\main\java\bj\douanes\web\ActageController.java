package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.ActFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/actage")
public class ActageController {
    
    @Autowired
    private ActFacade actFacade;

    @GetMapping
    public ResponseEntity<?> getAllActage() {
        return AppResponse.ok(actFacade.getActageAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getActeById(@PathVariable Long id) {
        return AppResponse.ok(actFacade.getActageById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createActage(@RequestBody DTO.ActDto actDto,@PathVariable Long agentId) {
        return AppResponse.created(actFacade.createActage(agentId, actDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateActe(@RequestBody DTO.ActDto actDto,@PathVariable Long id){
        return AppResponse.ok(actFacade.update(id, actDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteActe(@PathVariable Long id){
        return AppResponse.ok(actFacade.deleteActage(id)); 
    }
}
