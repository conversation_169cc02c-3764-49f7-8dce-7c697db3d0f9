package bj.douanes.web;

import java.util.List;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.auth.dto.AssignPermissionDto;
import bj.douanes.core.auth.dto.PermissionCreateDto;
import bj.douanes.core.auth.dto.PermissionDto;
import bj.douanes.core.auth.dto.UserPermissionDto;
import bj.douanes.core.auth.model.Permission;
import bj.douanes.core.auth.service.PermissionService;
import bj.douanes.core.shared.response.AppResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("api/permissions")
@Tag(name = "Gestion des Permissions", description = "API pour la gestion des permissions RBAC")
public class PermissionController {

    private final PermissionService permissionService;

    @GetMapping
    @PreAuthorize("hasAuthority('READ_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Lister toutes les permissions", description = "Récupère la liste de toutes les permissions disponibles")
    public ResponseEntity<?> getAllPermissions() {
        log.info("Récupération de toutes les permissions");
        List<Permission> permissions = permissionService.findAll();
        return AppResponse.ok(permissions, "Permissions récupérées avec succès");
    }

    @GetMapping("/search")
    @PreAuthorize("hasAuthority('READ_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Rechercher des permissions", description = "Recherche des permissions par nom ou description")
    public ResponseEntity<?> searchPermissions(@RequestParam String term) {
        log.info("Recherche de permissions avec le terme: {}", term);
        List<Permission> permissions = permissionService.searchPermissions(term);
        return AppResponse.ok(permissions, "Recherche effectuée avec succès");
    }

    @GetMapping("/{name}")
    @PreAuthorize("hasAuthority('READ_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Obtenir une permission par nom", description = "Récupère les détails d'une permission spécifique")
    public ResponseEntity<?> getPermissionByName(@PathVariable String name) {
        log.info("Récupération de la permission: {}", name);
        Permission permission = permissionService.findByName(name)
                .orElseThrow(() -> new RuntimeException("Permission non trouvée: " + name));
        return AppResponse.ok(permission, "Permission trouvée");
    }

    @PostMapping
    @PreAuthorize("hasAuthority('WRITE_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Créer une nouvelle permission", description = "Crée une nouvelle permission dans le système")
    public ResponseEntity<?> createPermission(@Valid @RequestBody PermissionCreateDto createDto) {
        log.info("Création d'une nouvelle permission: {}", createDto.getName());
        Permission permission = permissionService.createPermission(createDto.getName(), createDto.getDescription());
        return AppResponse.created(permission, "Permission créée avec succès");
    }

    @PutMapping("/{name}")
    @PreAuthorize("hasAuthority('WRITE_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Mettre à jour une permission", description = "Met à jour la description d'une permission existante")
    public ResponseEntity<?> updatePermission(@PathVariable String name, @Valid @RequestBody PermissionCreateDto updateDto) {
        log.info("Mise à jour de la permission: {}", name);
        Permission permission = permissionService.updatePermission(name, updateDto.getDescription());
        return AppResponse.ok(permission, "Permission mise à jour avec succès");
    }

    @DeleteMapping("/{name}")
    @PreAuthorize("hasAuthority('DELETE_PERMISSIONS') or hasRole('ADMIN')")
    @Operation(summary = "Supprimer une permission", description = "Supprime une permission du système")
    public ResponseEntity<?> deletePermission(@PathVariable String name) {
        log.info("Suppression de la permission: {}", name);
        permissionService.deletePermission(name);
        return AppResponse.ok("Permission supprimée avec succès");
    }

    // ==================== GESTION DES PERMISSIONS UTILISATEUR ====================

    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('READ_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Obtenir les permissions d'un utilisateur", description = "Récupère toutes les permissions d'un utilisateur (directes + rôles)")
    public ResponseEntity<?> getUserPermissions(@PathVariable UUID userId) {
        log.info("Récupération des permissions de l'utilisateur: {}", userId);
        
        // Créer un DTO avec toutes les informations de permissions
        UserPermissionDto userPermissions = UserPermissionDto.builder()
                .userId(userId)
                .directPermissions(permissionService.getAllUserPermissions(userId))
                .build();
        
        return AppResponse.ok(userPermissions, "Permissions utilisateur récupérées avec succès");
    }

    @PostMapping("/user/assign")
    @PreAuthorize("hasAuthority('WRITE_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Assigner des permissions à un utilisateur", description = "Assigne une ou plusieurs permissions directement à un utilisateur")
    public ResponseEntity<?> assignPermissionsToUser(@Valid @RequestBody AssignPermissionDto assignDto) {
        log.info("Attribution des permissions {} à l'utilisateur {}", 
                assignDto.getPermissionNames(), assignDto.getUserId());
        
        for (String permissionName : assignDto.getPermissionNames()) {
            permissionService.assignPermissionToUser(assignDto.getUserId(), permissionName);
        }
        
        return AppResponse.ok("Permissions assignées avec succès");
    }

    @DeleteMapping("/user/{userId}/permission/{permissionName}")
    @PreAuthorize("hasAuthority('WRITE_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Retirer une permission d'un utilisateur", description = "Retire une permission directe d'un utilisateur")
    public ResponseEntity<?> removePermissionFromUser(@PathVariable UUID userId, @PathVariable String permissionName) {
        log.info("Retrait de la permission '{}' de l'utilisateur {}", permissionName, userId);
        permissionService.removePermissionFromUser(userId, permissionName);
        return AppResponse.ok("Permission retirée avec succès");
    }

    // ==================== GESTION DES PERMISSIONS DE RÔLE ====================

    @GetMapping("/role/{roleName}")
    @PreAuthorize("hasAuthority('READ_ROLES') or hasRole('ADMIN')")
    @Operation(summary = "Obtenir les permissions d'un rôle", description = "Récupère les permissions par défaut d'un rôle")
    public ResponseEntity<?> getRolePermissions(@PathVariable String roleName) {
        log.info("Récupération des permissions du rôle: {}", roleName);
        List<Permission> permissions = permissionService.findRolePermissions(roleName);
        return AppResponse.ok(permissions, "Permissions du rôle récupérées avec succès");
    }

    @PostMapping("/role/{roleName}/permission/{permissionName}")
    @PreAuthorize("hasAuthority('WRITE_ROLES') or hasRole('ADMIN')")
    @Operation(summary = "Assigner une permission à un rôle", description = "Assigne une permission par défaut à un rôle")
    public ResponseEntity<?> assignPermissionToRole(@PathVariable String roleName, @PathVariable String permissionName) {
        log.info("Attribution de la permission '{}' au rôle '{}'", permissionName, roleName);
        permissionService.assignPermissionToRole(roleName, permissionName);
        return AppResponse.ok("Permission assignée au rôle avec succès");
    }

    @DeleteMapping("/role/{roleName}/permission/{permissionName}")
    @PreAuthorize("hasAuthority('WRITE_ROLES') or hasRole('ADMIN')")
    @Operation(summary = "Retirer une permission d'un rôle", description = "Retire une permission par défaut d'un rôle")
    public ResponseEntity<?> removePermissionFromRole(@PathVariable String roleName, @PathVariable String permissionName) {
        log.info("Retrait de la permission '{}' du rôle '{}'", permissionName, roleName);
        permissionService.removePermissionFromRole(roleName, permissionName);
        return AppResponse.ok("Permission retirée du rôle avec succès");
    }

    // ==================== INITIALISATION ====================

    @PostMapping("/initialize")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Initialiser les permissions par défaut", description = "Initialise les permissions par défaut du système")
    public ResponseEntity<?> initializeDefaultPermissions() {
        log.info("Initialisation des permissions par défaut");
        permissionService.initializeDefaultPermissions();
        return AppResponse.ok("Permissions par défaut initialisées avec succès");
    }
}
