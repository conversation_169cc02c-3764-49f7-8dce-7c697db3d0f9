package bj.douanes.core.auth.model;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"users", "defaultPermissions"})
@ToString(exclude = {"users", "defaultPermissions"})
@Entity
@Table(name = "roles")
public class Role {
    
    @Id
    @Column(length = 50)
    private String name;
    
    @Column(length = 255)
    private String description;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    // Relation OneToMany avec User (un rôle peut avoir plusieurs utilisateurs)
    @OneToMany(mappedBy = "role")
    @Builder.Default
    private Set<User> users = new HashSet<>();

    // Permissions par défaut associées à ce rôle
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_name"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    @Builder.Default
    private Set<Permission> defaultPermissions = new HashSet<>();
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Constructeur de convenance
    public Role(String name, String description) {
        this.name = name;
        this.description = description;
        this.defaultPermissions = new HashSet<>();
    }

    // Méthodes utilitaires pour la gestion des permissions
    public void addDefaultPermission(Permission permission) {
        this.defaultPermissions.add(permission);
    }

    public void removeDefaultPermission(Permission permission) {
        this.defaultPermissions.remove(permission);
    }

    public boolean hasDefaultPermission(String permissionName) {
        return defaultPermissions.stream()
                .anyMatch(permission -> permission.getName().equals(permissionName));
    }

    public Set<String> getDefaultPermissionNames() {
        return defaultPermissions.stream()
                .map(Permission::getName)
                .collect(java.util.stream.Collectors.toSet());
    }
}
