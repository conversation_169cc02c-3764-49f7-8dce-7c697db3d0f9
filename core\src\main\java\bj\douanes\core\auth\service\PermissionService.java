package bj.douanes.core.auth.service;


import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import bj.douanes.core.auth.model.Permission;
import bj.douanes.core.auth.model.Role;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.repository.PermissionRepository;
import bj.douanes.core.auth.repository.RoleRepository;
import bj.douanes.core.auth.repository.UserRepository;
import bj.douanes.core.shared.error.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface PermissionService {
    
    /**
     * Créer une nouvelle permission
     */
    Permission createPermission(String name, String description);
    
    /**
     * Mettre à jour une permission existante
     */
    Permission updatePermission(String name, String newDescription);
    
    /**
     * Supprimer une permission
     */
    void deletePermission(String name);
    
    /**
     * Rechercher une permission par son nom
     */
    Optional<Permission> findByName(String name);
    
    /**
     * Rechercher toutes les permissions
     */
    List<Permission> findAll();
    
    /**
     * Rechercher des permissions par leurs noms
     */
    List<Permission> findByNames(Set<String> names);
    
    /**
     * Rechercher les permissions d'un utilisateur
     */
    List<Permission> findUserPermissions(UUID userId);
    
    /**
     * Rechercher les permissions par défaut d'un rôle
     */
    List<Permission> findRolePermissions(String roleName);
    
    /**
     * Assigner une permission à un utilisateur
     */
    void assignPermissionToUser(UUID userId, String permissionName);
    
    /**
     * Retirer une permission d'un utilisateur
     */
    void removePermissionFromUser(UUID userId, String permissionName);
    
    /**
     * Assigner une permission par défaut à un rôle
     */
    void assignPermissionToRole(String roleName, String permissionName);
    
    /**
     * Retirer une permission par défaut d'un rôle
     */
    void removePermissionFromRole(String roleName, String permissionName);
    
    /**
     * Vérifier si une permission existe
     */
    boolean existsByName(String name);
    
    /**
     * Rechercher des permissions par terme de recherche
     */
    List<Permission> searchPermissions(String searchTerm);
    
    /**
     * Obtenir toutes les permissions d'un utilisateur (directes + rôles)
     */
    Set<String> getAllUserPermissions(UUID userId);
    
    /**
     * Initialiser les permissions par défaut du système
     */
    void initializeDefaultPermissions();
}

@Slf4j
@Service
@RequiredArgsConstructor
class PermissionServiceImpl implements PermissionService {

    private final PermissionRepository permissionRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    @Override
    @Transactional
    public Permission createPermission(String name, String description) {
        log.info("Création de la permission: {} - {}", name, description);

        if (permissionRepository.existsByName(name)) {
            throw new ApiException("La permission '" + name + "' existe déjà");
        }

        Permission permission = Permission.builder()
                .name(name.toUpperCase())
                .description(description)
                .build();

        return permissionRepository.save(permission);
    }

    @Override
    @Transactional
    public Permission updatePermission(String name, String newDescription) {
        log.info("Mise à jour de la permission: {}", name);

        Permission permission = permissionRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Permission '" + name + "' non trouvée"));

        permission.setDescription(newDescription);
        return permissionRepository.save(permission);
    }

    @Override
    @Transactional
    public void deletePermission(String name) {
        log.info("Suppression de la permission: {}", name);

        Permission permission = permissionRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Permission '" + name + "' non trouvée"));

        permissionRepository.delete(permission);
    }

    @Override
    public Optional<Permission> findByName(String name) {
        return permissionRepository.findByName(name);
    }

    @Override
    public List<Permission> findAll() {
        return permissionRepository.findAllByOrderByNameAsc();
    }

    @Override
    public List<Permission> findByNames(Set<String> names) {
        return permissionRepository.findByNameIn(names);
    }

    @Override
    public List<Permission> findUserPermissions(UUID userId) {
        return permissionRepository.findByUserId(userId);
    }

    @Override
    public List<Permission> findRolePermissions(String roleName) {
        return permissionRepository.findByRoleName(roleName);
    }

    @Override
    @Transactional
    public void assignPermissionToUser(UUID userId, String permissionName) {
        log.info("Attribution de la permission '{}' à l'utilisateur {}", permissionName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        Permission permission = permissionRepository.findByName(permissionName)
                .orElseThrow(() -> new ApiException("Permission '" + permissionName + "' non trouvée"));

        if (user.hasPermission(permissionName)) {
            log.warn("L'utilisateur {} a déjà la permission '{}'", userId, permissionName);
            return;
        }

        user.addPermission(permission);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void removePermissionFromUser(UUID userId, String permissionName) {
        log.info("Retrait de la permission '{}' de l'utilisateur {}", permissionName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        Permission permission = permissionRepository.findByName(permissionName)
                .orElseThrow(() -> new ApiException("Permission '" + permissionName + "' non trouvée"));

        user.removePermission(permission);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void assignPermissionToRole(String roleName, String permissionName) {
        log.info("Attribution de la permission '{}' au rôle '{}'", permissionName, roleName);

        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new ApiException("Rôle '" + roleName + "' non trouvé"));

        Permission permission = permissionRepository.findByName(permissionName)
                .orElseThrow(() -> new ApiException("Permission '" + permissionName + "' non trouvée"));

        if (role.hasDefaultPermission(permissionName)) {
            log.warn("Le rôle '{}' a déjà la permission '{}'", roleName, permissionName);
            return;
        }

        role.addDefaultPermission(permission);
        roleRepository.save(role);
    }

    @Override
    @Transactional
    public void removePermissionFromRole(String roleName, String permissionName) {
        log.info("Retrait de la permission '{}' du rôle '{}'", permissionName, roleName);

        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new ApiException("Rôle '" + roleName + "' non trouvé"));

        Permission permission = permissionRepository.findByName(permissionName)
                .orElseThrow(() -> new ApiException("Permission '" + permissionName + "' non trouvée"));

        role.removeDefaultPermission(permission);
        roleRepository.save(role);
    }

    @Override
    public boolean existsByName(String name) {
        return permissionRepository.existsByName(name);
    }

    @Override
    public List<Permission> searchPermissions(String searchTerm) {
        return permissionRepository.findByNameOrDescriptionContainingIgnoreCase(searchTerm);
    }

    @Override
    public Set<String> getAllUserPermissions(UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));

        return user.getAllPermissionNames();
    }

    @Override
    @Transactional
    public void initializeDefaultPermissions() {
        log.info("Initialisation des permissions par défaut du système");

        // Permissions de base pour la gestion des utilisateurs
        createPermissionIfNotExists("READ_USERS", "Lire les informations des utilisateurs");
        createPermissionIfNotExists("WRITE_USERS", "Créer et modifier les utilisateurs");
        createPermissionIfNotExists("DELETE_USERS", "Supprimer les utilisateurs");

        // Permissions pour la gestion des rôles
        createPermissionIfNotExists("READ_ROLES", "Lire les informations des rôles");
        createPermissionIfNotExists("WRITE_ROLES", "Créer et modifier les rôles");
        createPermissionIfNotExists("DELETE_ROLES", "Supprimer les rôles");

        // Permissions pour la gestion des permissions
        createPermissionIfNotExists("READ_PERMISSIONS", "Lire les informations des permissions");
        createPermissionIfNotExists("WRITE_PERMISSIONS", "Créer et modifier les permissions");
        createPermissionIfNotExists("DELETE_PERMISSIONS", "Supprimer les permissions");

        // Permissions pour l'administration système
        createPermissionIfNotExists("ADMIN_SYSTEM", "Administration complète du système");
        createPermissionIfNotExists("VIEW_LOGS", "Consulter les logs système");
        createPermissionIfNotExists("MANAGE_CONFIG", "Gérer la configuration système");

        log.info("Permissions par défaut initialisées avec succès");
    }

    private void createPermissionIfNotExists(String name, String description) {
        if (!permissionRepository.existsByName(name)) {
            createPermission(name, description);
            log.debug("Permission '{}' créée", name);
        }
    }
}
