# Étape 1 : Build de l'application
FROM maven:3.9.6-eclipse-temurin-17 AS build
WORKDIR /app
# COPY pom.xml .
COPY . .
RUN mvn clean package -DskipTests

# Étape 2 : Image de production
FROM eclipse-temurin:17-jre
WORKDIR /app
COPY --from=build /app/web/target/*.jar app.jar

# Configuration du profil Spring (par défaut prod)
ENV SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}

# Configuration des bases de données
ENV DB_DGD_USERNAME=${DB_DGD_USERNAME:-root}
ENV DB_DGD_PASSWORD=${DB_DGD_PASSWORD}
ENV DB_DGD_URL=${DB_DGD_URL:-************************************}

ENV DB_HOME_USERNAME=${DB_HOME_USERNAME:-root}
ENV DB_HOME_PASSWORD=${DB_HOME_PASSWORD}
ENV DB_HOME_URL=${DB_HOME_URL:-*************************************}

ENV DB_TEST_USERNAME=${DB_TEST_USERNAME:-root}
ENV DB_TEST_PASSWORD=${DB_TEST_PASSWORD}
ENV DB_TEST_URL=${DB_TEST_URL:-*************************************}

# Configuration JWT
ENV JWT_SECRET_KEY=${JWT_SECRET_KEY:-9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898F}
ENV JWT_DELAY=${JWT_DELAY:-24}

# Configuration Mail
ENV MAIL_HOST=${MAIL_HOST:-mail.finances.bj}
ENV MAIL_PORT=${MAIL_PORT:-587}
ENV MAIL_USERNAME=${MAIL_USERNAME:-<EMAIL>}
ENV MAIL_PASSWORD=${MAIL_PASSWORD}

# Configuration sécurité
ENV SECURITY_WHITE_LIST=${SECURITY_WHITE_LIST:-/,/v3/api-docs/**,/swagger-ui/**,/api/auth/**}

# Configuration logs
ENV LOG_LEVEL_DOUANES=${LOG_LEVEL_DOUANES:-info}
ENV LOG_LEVEL_ROOT=${LOG_LEVEL_ROOT:-warn}

EXPOSE 80

ENTRYPOINT ["java", "-jar", "app.jar", "--server.port=80"]
