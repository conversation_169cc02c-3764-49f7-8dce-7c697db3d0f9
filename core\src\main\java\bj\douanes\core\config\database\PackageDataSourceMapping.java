package bj.douanes.core.config.database;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.PostConstruct;

/**
 * Configuration pour mapper les packages aux datasources
 * Nouvelle approche : lit app.datasource.{datasource}.packages au lieu de
 * app.datasource.mapping.packages.*
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PackageDataSourceMapping {

    private final Environment env;
    private final DatabaseComponent dbComponent;

    /**
     * Mapping package -> datasource construit à partir de la configuration
     * app.datasource.{datasource}.packages
     */
    private Map<String, String> packages = new HashMap<>();

    /**
     * Initialise le mapping des packages à partir de la configuration
     * Lit app.datasource.{datasource}.packages pour chaque datasource
     */
    @PostConstruct
    public void initializePackageMapping() {
        // Pour chaque datasource configurée
        for (String datasourceName : dbComponent.getEnvTypes()) {
            String packagesValue = env.getProperty(String.format("app.datasource.%s.packages", datasourceName));

            if (packagesValue != null && !packagesValue.trim().isEmpty()) {
                // Séparer les packages par virgule et les mapper à cette datasource
                String[] packageArray = packagesValue.split(",");
                for (String packageName : packageArray) {
                    String trimmedPackage = packageName.trim();
                    if (!trimmedPackage.isEmpty()) {
                        packages.put(trimmedPackage, datasourceName);
                    }
                }
            }
        }

        log.info("Mapping des packages initialise avec {} entrees", packages.size());
        if (log.isDebugEnabled()) {
            packages.forEach((pkg, ds) -> log.debug("  {} -> {}", pkg, ds));
        }
    }

    /**
     * Détermine la datasource à utiliser en fonction du nom de classe complet
     * 
     * @param className Le nom complet de la classe (package + nom de classe)
     * @return Le nom de la datasource à utiliser
     */
    public String getDataSourceForClass(String className) {
        // Recherche du mapping le plus spécifique (package le plus long qui correspond)
        String bestMatch = null;
        String bestDataSource = dbComponent.getFirstType();

        for (Map.Entry<String, String> entry : packages.entrySet()) {
            String packageName = entry.getKey();
            String dataSource = entry.getValue();

            if (className.startsWith(packageName) && (bestMatch == null || packageName.length() > bestMatch.length())) {
                bestMatch = packageName;
                bestDataSource = dataSource;
            }

        }

        log.debug("Classe {} mappée vers datasource {}", className, bestDataSource);
        return bestDataSource;
    }

    /**
     * Détermine la datasource à utiliser en fonction du package
     * 
     * @param packageName Le nom du package
     * @return Le nom de la datasource à utiliser
     */
    public String getDataSourceForPackage(String packageName) {
        return packages.getOrDefault(packageName, dbComponent.getFirstType());
    }
}
