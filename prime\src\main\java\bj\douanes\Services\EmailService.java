package bj.douanes.Services;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import bj.douanes.DTO.BanqueInfoDTO3;
import bj.douanes.Model.AgentTSD;
import bj.douanes.Model.PrimeTsdAgent;
import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.Repartition;
import bj.douanes.Repository.PrimeTsdAgentRepository;
import bj.douanes.Repository.RAgentBanqueRepository;
import bj.douanes.Repository.RAgentRepartitionRepository;
import bj.douanes.Repository.RepartitionRepo;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private TemplateEngine templateEngine;

    private final RAgentRepartitionRepository rAgentRepartitionRepository;
    private final PrimeTsdAgentRepository primeTsdAgentRepository;
    private final RepartitionRepo repartitionRepository;
    private final RAgentBanqueRepository rAgentBanqueRepository;

    private static final Logger log = LoggerFactory.getLogger(EmailService.class);
    
    public void sendOrdreVirementEmailToAllAgents(Long idRepartition) throws MessagingException {
        List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);
        if (repartitions.isEmpty()) return;

        Repartition repartitionInfo = repartitionRepository.findByIdRepartition(idRepartition);
        String typeP = repartitionInfo != null ? repartitionInfo.getCodeTypePrime() : "Inconnu";
        String periode = repartitionInfo != null ? repartitionInfo.getPeriode() + " " + repartitionInfo.getAnnee() : "Inconnue";

        List<String> matricules = repartitions.stream()
                .map(r -> r.getAgent().getMatricule())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<String, BanqueInfoDTO3> ribMap = rAgentBanqueRepository.findBanqueInfosByMatricules(matricules)
        .stream()
        .collect(Collectors.toMap(BanqueInfoDTO3::matricule, Function.identity()));


        Map<String, PrimeTsdAgent> primeMap = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
                .collect(Collectors.toMap(p -> p.getRAgentRepartition().getAgent().getMatricule(), p -> p));

        for (RAgentRepartition repartition : repartitions) {
            AgentTSD agent = repartition.getAgent();
            if (agent == null || agent.getEmail() == null || agent.getEmail().isEmpty()) continue;

            BanqueInfoDTO3 banqueInfo = ribMap.get(agent.getMatricule());
            PrimeTsdAgent prime = primeMap.get(agent.getMatricule());

            sendEmailToAgent(agent, banqueInfo, prime, typeP, periode);
        }
    }

    private void sendEmailToAgent(AgentTSD agent, BanqueInfoDTO3 banqueInfo, PrimeTsdAgent prime, String typeP, String periode) {
        try {
            String rib = banqueInfo != null ? banqueInfo.rib() : null;
            BigDecimal montant = prime != null ? prime.getMontantNetPayer() : BigDecimal.ZERO;

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());

            helper.setTo(agent.getEmail());
            helper.setSubject("Notification de virement à venir");
            helper.setFrom("<EMAIL>");

            Context context = new Context();
            context.setVariable("Nom", agent.getNom());
            context.setVariable("Prenoms", agent.getPrenoms());
            context.setVariable("montant", montant);
            context.setVariable("rib", rib);
            context.setVariable("typePrime", typeP);
            context.setVariable("periode", periode);

            String html = templateEngine.process("emailTemplate", context);
            helper.setText(html, true);

            mailSender.send(message);
        } catch (MessagingException e) {
            // Log erreur avec matricule ou email concerné
            log.error("Erreur d'envoi d'email à l'agent : " + agent.getMatricule(), e);
        }
    } 





    // public void sendOrdreVirementEmailToAllAgents(Long idRepartition) throws MessagingException {
    //     List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);
    //     Repartition repartitionInfo = repartitionRepository.findByIdRepartition(idRepartition);

    //     String typeP;
    //     String periode;   

    //     if(repartitionInfo != null)
    //     {
    //         typeP = repartitionInfo.getCodeTypePrime();
    //         periode = repartitionInfo.getPeriode() + " " + repartitionInfo.getAnnee();
    //     }else{
    //         typeP = "Inconnu";
    //         periode = "Inconnue";
    //     }
    //     for (RAgentRepartition repartition : repartitions) {
    //         AgentTSD agent = repartition.getAgent();
            
    //         if (agent == null || agent.getEmail() == null || agent.getEmail().isEmpty()) {
    //             continue; 
    //         }
    //         String rib = null;
    //         List<BanqueInfoDTO2> agentBanque = rAgentBanqueRepository.findBanqueInfosByMatricule2(agent.getMatricule());
    //         if (agentBanque != null && !agentBanque.isEmpty()) {
    //             rib = agentBanque.get(0).rib();
    //         }
    //         PrimeTsdAgent prime = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
    //                  .filter(p -> p.getRAgentRepartition().getAgent().getMatricule().equals(agent.getMatricule()))
    //                  .findFirst()
    //                  .orElse(null);
    //         BigDecimal montant = BigDecimal.ZERO;
    //         if (prime != null) {
    //             montant = prime.getMontantNetPayer();
    //         }
    //         MimeMessage message = mailSender.createMimeMessage();
    //         MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());

    //         helper.setTo(repartition.getAgent().getEmail());
    //         helper.setSubject("Notification de virement à venir");
    //         helper.setFrom("f6fa66edc88fe4");

    //         Context context = new Context();
    //         context.setVariable("Nom", agent.getNom());
    //         context.setVariable("Prenoms", agent.getPrenoms());
    //         context.setVariable("montant", montant);
    //         context.setVariable("rib", rib);
    //         context.setVariable("typePrime", typeP);
    //         context.setVariable("periode", periode);


    //         String html = templateEngine.process("emailTemplate", context);

    //         helper.setText(html, true);

    //         mailSender.send(message);
    //     }
    // }

}
