package bj.douanes.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.NotationFacade;
import bj.douanes.facade.UTILS.DTO;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/agents/notation")
public class NotationController {
    
    @Autowired
    private NotationFacade notationFacade;

    @GetMapping
    public ResponseEntity<?> getAllNotes() {
        return AppResponse.ok(notationFacade.getNotationAllList());
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getNotesById(@PathVariable Long id) {
        return AppResponse.ok(notationFacade.getNotationById(id));
    }

    @PostMapping("/{agentId}")
    public ResponseEntity<?> createNotes(@RequestBody DTO.NotationDto notationDto, @PathVariable Long agentId) {
        return AppResponse.created(notationFacade.createNotation(agentId, notationDto));
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updatedNote(@RequestBody DTO.NotationDto notationDto,@PathVariable Long id){
        return AppResponse.ok(notationFacade.update(id, notationDto)); 
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteNote(@PathVariable Long id){
        return AppResponse.ok(notationFacade.deleteNotation(id)); 
    }
}
