package bj.douanes.core.shared.response;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import bj.douanes.core.shared.dto.AppReqResDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public final class AppResponse<T> extends AppReqResDto {

    // Messages constants
    private static final String SUCCESS_MESSAGE = "Requête traitée avec succès";
    private static final String CREATED_MESSAGE = "Ressource créée avec succès";
    private static final String ACCEPTED_MESSAGE = "Requête acceptée";
    private static final String NO_CONTENT_MESSAGE = "Aucun contenu";
    private static final String FILE_DOWNLOAD_MESSAGE = "Fichier téléchargé avec succès";

    private T data;
    private String fileName;

    // Constructeurs simplifiés
    public AppResponse() {
        super();
        setMessage(SUCCESS_MESSAGE);
        setStatusCode(HttpStatus.OK);
    }

    public AppResponse(T data) {
        this();
        this.data = data;
    }

    public AppResponse(String message, HttpStatus status) {
        super();
        setMessage(message);
        setStatusCode(status);
    }

    public AppResponse(T data, String message, HttpStatus status) {
        this(message, status);
        this.data = data;
    }

    // ===== MÉTHODES STATIQUES POUR RÉPONSES COURANTES =====

    /**
     * Réponse de succès avec données
     */
    public static <T> ResponseEntity<AppResponse<T>> ok(T data) {
        return ResponseEntity.ok(new AppResponse<>(data));
    }

    /**
     * Réponse de succès avec données et message personnalisé
     */
    public static <T> ResponseEntity<AppResponse<T>> ok(T data, String message) {
        return ResponseEntity.ok(new AppResponse<>(data, message, HttpStatus.OK));
    }

    /**
     * Réponse générique avec statut et message
     */
    public static <T> ResponseEntity<AppResponse<T>> of(String message, HttpStatus status) {
        return ResponseEntity.status(status).body(new AppResponse<>(message, status));
    }

    /**
     * Réponse générique avec données, message et statut
     */
    public static <T> ResponseEntity<AppResponse<T>> of(T data, String message, HttpStatus status) {
        return ResponseEntity.status(status).body(new AppResponse<>(data, message, status));
    }

    /**
     * Réponse de création sans données
     */
    public static ResponseEntity<AppResponse<Object>> created() {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new AppResponse<>(CREATED_MESSAGE, HttpStatus.CREATED));
    }

    /**
     * Réponse de création sans données avec message
     */
    public static ResponseEntity<AppResponse<Object>> created(String message) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new AppResponse<>(message, HttpStatus.CREATED));
    }

    /**
     * Réponse de création avec données
     */
    public static <T> ResponseEntity<AppResponse<T>> created(T data) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new AppResponse<>(data, CREATED_MESSAGE, HttpStatus.CREATED));
    }
    
    /**
     * Réponse de création avec données et message
     */
    public static <T> ResponseEntity<AppResponse<T>> created(T data, String message) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new AppResponse<>(data, message, HttpStatus.CREATED));
    }

    /**
     * Réponse acceptée
     */
    public static ResponseEntity<AppResponse<Object>> accepted() {
        return ResponseEntity.status(HttpStatus.ACCEPTED)
                .body(new AppResponse<>(ACCEPTED_MESSAGE, HttpStatus.ACCEPTED));
    }

    /**
     * Réponse d'erreur de requête
     */
    public static ResponseEntity<AppResponse<Object>> badRequest(String message) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new AppResponse<>(message, HttpStatus.BAD_REQUEST));
    }

    /**
     * Réponse avec fichier téléchargeable
     */
    public static ResponseEntity<AppResponse<byte[]>> okWithFile(byte[] fileData, String fileName) {
        AppResponse<byte[]> response = new AppResponse<>(fileData, FILE_DOWNLOAD_MESSAGE, HttpStatus.OK);
        response.setFileName(fileName);
        return ResponseEntity.ok(response);
    }

    /**
     * Réponse d'erreur serveur interne
     */
    public static ResponseEntity<AppResponse<Object>> internalServerError(String message) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new AppResponse<>(message, HttpStatus.INTERNAL_SERVER_ERROR));
    }

    /**
     * Réponse sans contenu
     */
    public static ResponseEntity<AppResponse<Object>> noContent() {
        return ResponseEntity.status(HttpStatus.NO_CONTENT)
                .body(new AppResponse<>(NO_CONTENT_MESSAGE, HttpStatus.NO_CONTENT));
    }

    /**
     * Réponse non trouvée
     */
    public static ResponseEntity<AppResponse<Object>> notFound(String message) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new AppResponse<>(message, HttpStatus.NOT_FOUND));
    }

    /**
     * Réponse non autorisée
     */
    public static ResponseEntity<AppResponse<Object>> unauthorized(String message) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new AppResponse<>(message, HttpStatus.UNAUTHORIZED));
    }

    /**
     * Réponse interdite
     */
    public static ResponseEntity<AppResponse<Object>> forbidden(String message) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(new AppResponse<>(message, HttpStatus.FORBIDDEN));
    }
}