package bj.douanes.DTO;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgentCreationDto2 {
    
    private String matricule;
    private String nom;
    private String prenoms;
    private String sexe; // 'M' ou 'F'
    private LocalDate dateNais;
    private String ifu;
    private String telephone;
    private String email;
    private Boolean actif = true;

    private List<AgentUniteDto> unites;
    private List<AgentFonctionDto> fonctions;
    private List<AgentBanqueDto> banques;

    @Data
    public static class AgentUniteDto {
        private String codeUnite;
        private LocalDate dateDebut;
        private LocalDate dateFin;
    }

    @Data
    public static class AgentFonctionDto {
        private String codeFonction;
        private LocalDate dateDebut;
        private LocalDate dateFin;
    }

    @Data
    public static class AgentBanqueDto {
        private String codeBanque;
        private String rib;
        private LocalDate dateDebut;
        private LocalDate dateFin;

        private BanqueDto banque;  
    }

    @Data
    public static class BanqueDto {
        private String codeBanque;
        private String nomBanque;
    }
}
