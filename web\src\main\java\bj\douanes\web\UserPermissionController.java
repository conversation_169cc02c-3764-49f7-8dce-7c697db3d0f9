package bj.douanes.web;

import java.util.Set;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.auth.dto.UserPermissionDto;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.service.CrudService;
import bj.douanes.core.auth.service.PermissionService;
import bj.douanes.core.shared.response.AppResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("api/users")
@Tag(name = "Gestion Utilisateurs avec Permissions", description = "API pour la gestion avancée des utilisateurs et leurs permissions")
public class UserPermissionController {

    private final CrudService crudService;
    private final PermissionService permissionService;

    @GetMapping("/{userId}/permissions/detailed")
    @PreAuthorize("hasAuthority('READ_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Obtenir les permissions détaillées d'un utilisateur", 
               description = "Récupère toutes les informations de permissions d'un utilisateur (directes, rôles, héritées)")
    public ResponseEntity<?> getUserDetailedPermissions(@PathVariable UUID userId) {
        log.info("Récupération des permissions détaillées de l'utilisateur: {}", userId);
        
        User user = crudService.findUserById(userId);
        
        // Construire le DTO avec toutes les informations
        UserPermissionDto userPermissions = UserPermissionDto.builder()
                .userId(user.getId())
                .email(user.getEmail())
                .matricule(user.getMatricule())
                .role(user.getRoleName())
                .directPermissions(user.getPermissionNames())
                .rolePermissions(getRolePermissions(user))
                .allPermissions(user.getAllPermissionNames())
                .build();
        
        return AppResponse.ok(userPermissions, "Permissions détaillées récupérées avec succès");
    }

    @GetMapping("/{userId}/authorities")
    @PreAuthorize("hasAuthority('READ_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Obtenir toutes les autorités d'un utilisateur", 
               description = "Récupère toutes les autorités Spring Security d'un utilisateur (rôles avec ROLE_ + permissions)")
    public ResponseEntity<?> getUserAuthorities(@PathVariable UUID userId) {
        log.info("Récupération des autorités de l'utilisateur: {}", userId);
        
        User user = crudService.findUserById(userId);
        Set<String> authorities = user.getAuthorities().stream()
                .map(auth -> auth.getAuthority())
                .collect(java.util.stream.Collectors.toSet());
        
        return AppResponse.ok(authorities, "Autorités récupérées avec succès");
    }

    @PostMapping("/{userId}/permissions/sync")
    @PreAuthorize("hasAuthority('WRITE_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Synchroniser les permissions d'un utilisateur", 
               description = "Synchronise les permissions d'un utilisateur avec celles de ses rôles")
    public ResponseEntity<?> syncUserPermissions(@PathVariable UUID userId) {
        log.info("Synchronisation des permissions de l'utilisateur: {}", userId);
        
        User user = crudService.findUserById(userId);
        
        // Récupérer les permissions actuelles
        Set<String> currentPermissions = user.getAllPermissionNames();
        
        // Log pour information
        log.info("Utilisateur {} - Rôle: {}, Permissions totales: {}", 
                user.getEmail(), user.getRoleName(), currentPermissions);
        
        return AppResponse.ok(currentPermissions, "Permissions synchronisées avec succès");
    }

    @GetMapping("/permissions/summary")
    @PreAuthorize("hasAuthority('READ_USERS') or hasRole('ADMIN')")
    @Operation(summary = "Résumé des permissions de tous les utilisateurs", 
               description = "Obtient un résumé des permissions pour tous les utilisateurs")
    public ResponseEntity<?> getAllUsersPermissionsSummary() {
        log.info("Récupération du résumé des permissions de tous les utilisateurs");
        
        var users = crudService.findAllUsers();
        var summary = users.stream()
                .map(user -> UserPermissionDto.builder()
                        .userId(user.getId())
                        .email(user.getEmail())
                        .matricule(user.getMatricule())
                        .role(user.getRoleName())
                        .allPermissions(user.getAllPermissionNames())
                        .build())
                .toList();
        
        return AppResponse.ok(summary, "Résumé des permissions récupéré avec succès");
    }

    /**
     * Méthode utilitaire pour récupérer les permissions héritées du rôle
     */
    private Set<String> getRolePermissions(User user) {
        if (user.getRoleName() != null) {
            return user.getRole().getDefaultPermissions().stream()
                    .map(permission -> permission.getName())
                    .collect(java.util.stream.Collectors.toSet());
        }
        return Set.of();
    }
}
