package bj.douanes.Model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "primetsd_unite")
public class PrimeTSDUnite {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_prime_unite")
    private Long idPrimeUnite;

    @Column(name = "montant_verset")
    private BigDecimal montantVerset = BigDecimal.ZERO;

    @Column(name = "oeuvre_social_unite")
    private BigDecimal oeuvreSocialUnite = BigDecimal.ZERO;

    @Column(name = "bonification_unite")
    private BigDecimal bonificationUnite = BigDecimal.ZERO;

    @Column(name = "part_unite")
    private BigDecimal partUnite = BigDecimal.ZERO;

    @Column(name = "cumul_coef")
    private BigDecimal cumulCoef = BigDecimal.ZERO;

    @Column(name = "created_at", nullable = false)
    private LocalDate createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDate updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
        @JoinColumn(name = "code_unite", referencedColumnName = "code_unite"),
        @JoinColumn(name = "id_repartition", referencedColumnName = "id_repartition")
    })
    @JsonIgnore
    private RUniteRepartition rUniteRepartition;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDate.now();
        updatedAt = LocalDate.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDate.now();
    }
}
